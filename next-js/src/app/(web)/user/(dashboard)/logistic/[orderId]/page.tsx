"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";

import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { ArrowRightIcon } from "lucide-react";

import { PageLayout } from "~/components/common/page-layout";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { useAuthContext } from "~/context/auth";
import { routes } from "~/lib/routes";
import {
  ConversationType,
  type DeliveryRequestStatus,
  type LogisticProviderProfileType,
  type PublicDeliveryRequestType,
  type SingleResponseType,
} from "~/lib/types";
import { cn, formatDate, formatPrice } from "~/lib/utils";
import { LogisticDetail } from "./_components/logistic-detail";

async function getDeliveryRequest({
  token,
  orderId,
}: {
  token: string | null;
  orderId: string;
}) {
  const response = await axios.get(
    `${routes.api.user.deliveryRequests.url(orderId)}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );

  return response.data;
}

export default function LogisticPage() {
  const params = useParams<{ orderId: string }>();

  const router = useRouter();

  const { token } = useAuthContext();

  const {
    data: deliveryRequestQuery,
    isLoading: deliveryRequestQueryIsLoading,
    isError: deliveryRequestQueryIsError,
  } = useQuery<
    SingleResponseType<{
      deliveryRequest: PublicDeliveryRequestType & {
        acceptedLogisticProvider: LogisticProviderProfileType | null;
      };
    }>
  >({
    queryKey: ["delivery-request", params.orderId],
    queryFn: () =>
      getDeliveryRequest({
        token,
        orderId: params.orderId,
      }),
  });

  const request = deliveryRequestQuery?.data?.deliveryRequest;

  const getStatusBadgeColor = (status: DeliveryRequestStatus) => {
    switch (status) {
      case "PENDING":
        return "bg-yellow-100 text-yellow-800 border-yellow-300";
      case "PROPOSED":
        return "bg-blue-100 text-blue-800 border-blue-300";
      case "PROCESSING":
        return "bg-purple-100 text-purple-800 border-purple-300";
      case "IN_TRANSIT":
        return "bg-indigo-100 text-indigo-800 border-indigo-300";
      case "DELIVERED":
        return "bg-green-100 text-green-800 border-green-300";
      default:
        return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  return (
    <PageLayout
      title={`Delivery Request ${request && `#${request.id.slice(0, 8)}`}`}
      description=""
      actions={
        request && (
          <Badge
            className={cn(
              "text-sm font-medium self-start md:self-center px-3 py-1",
              getStatusBadgeColor(request.status),
            )}
          >
            {request.status}
          </Badge>
        )
      }
      isLoading={deliveryRequestQueryIsLoading}
      isError={deliveryRequestQueryIsError}
      errorTitle="Error Loading Delivery Request"
      errorDescription="Failed to load delivery request. Please try again."
    >
      {request && (
        <div className="grid gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Request Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="p-4 bg-muted rounded-lg">
                  <p className="text-xs text-muted-foreground">Price</p>
                  <p className="font-medium text-lg">
                    {formatPrice(request.acceptedPrice ?? request.price)}
                  </p>
                </div>
                <div className="p-4 bg-muted rounded-lg">
                  <p className="text-xs text-muted-foreground">Last Updated</p>
                  <p className="font-medium">{formatDate(request.updatedAt)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          {request.acceptedLogisticProvider ? (
            <Card>
              <CardContent>
                <div className="flex items-end justify-between">
                  <div className="flex items-center gap-4">
                    <Avatar className="size-16">
                      <AvatarImage
                        src={`${process.env.NEXT_PUBLIC_FILE_URL}/${request.acceptedLogisticProvider.pictureId}`}
                        alt={request.acceptedLogisticProvider.name}
                        className="object-cover"
                      />
                      <AvatarFallback>
                        {request.acceptedLogisticProvider.name
                          .split(" ")
                          .map((part) => part.charAt(0).toUpperCase())
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-medium text-lg">
                        {request.acceptedLogisticProvider.name}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {request.acceptedLogisticProvider.phone}
                      </p>
                      <p className="text-sm mt-1">
                        {request.acceptedLogisticProvider.address},{" "}
                        {request.acceptedLogisticProvider.city},{" "}
                        {request.acceptedLogisticProvider.postalCode}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1"
                    onClick={() =>
                      router.push(
                        `${routes.app.user.conversations.url()}?type=${
                          ConversationType.LOGISTIC
                        }&profileId=${
                          // @ts-ignore
                          request.acceptedLogisticProvider.id
                        }&referenceId=${request.id}`,
                      )
                    }
                  >
                    Chat
                    <ArrowRightIcon className="size-3.5" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <LogisticDetail
              deliveryRequestId={request.id}
              acceptedLogisticProviderId={null}
            />
          )}
        </div>
      )}
    </PageLayout>
  );
}
