"use client";

import { useRouter, useSearchParams } from "next/navigation";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import axios, { AxiosError } from "axios";
import { CheckIcon, Loader2Icon, ShoppingCartIcon } from "lucide-react";
import { toast } from "sonner";

import { DataTable } from "~/components/table/data-table";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Button } from "~/components/ui/button";
import { useAuthContext } from "~/context/auth";
import { routes } from "~/lib/routes";
import type {
  LogisticProviderProfileType,
  MultipleResponseType,
  PublicAuthType,
  PublicLogisticProviderResponseType,
} from "~/lib/types";
import { cn, formatDate, formatPrice } from "~/lib/utils";

async function getLogisticProviderResponses({
  token,
  deliveryRequestId,
  page = 1,
  limit = 10,
  sort = "",
  minTotalPrice,
  maxTotalPrice,
}: {
  token: string | null;
  deliveryRequestId: string;
  page?: number;
  limit?: number;
  sort?: string;
  minTotalPrice?: number;
  maxTotalPrice?: number;
}) {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });

  if (sort) {
    params.append("sort", sort);
  }

  if (minTotalPrice !== undefined && minTotalPrice > 0) {
    params.append("minTotalPrice", minTotalPrice.toString());
  }

  if (maxTotalPrice !== undefined && maxTotalPrice > 0) {
    params.append("maxTotalPrice", maxTotalPrice.toString());
  }

  const response = await axios.get(
    `${routes.api.user.logisticProviderResponses.url(
      undefined,
      deliveryRequestId,
    )}?${params.toString()}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );

  return response.data;
}

async function acceptLogisticProviderResponse({
  token,
  responseId,
}: {
  token: string | null;
  responseId: string;
}) {
  const response = await axios.put(
    routes.api.user.logisticProviderResponses.url(responseId),
    {},
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );

  return response.data;
}

export function LogisticDetail({
  deliveryRequestId,
  acceptedLogisticProviderId,
}: {
  deliveryRequestId: string;
  acceptedLogisticProviderId: string | null;
}) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  const { token } = useAuthContext();

  const currentPage = Number(searchParams.get("page") || "1");
  const currentSort = searchParams.get("sort") || "";
  const currentMinTotalPrice = searchParams.get("minTotalPrice")
    ? Number(searchParams.get("minTotalPrice"))
    : undefined;
  const currentMaxTotalPrice = searchParams.get("maxTotalPrice")
    ? Number(searchParams.get("maxTotalPrice"))
    : undefined;

  const { data: logisticProviderResponsesQuery } = useQuery<
    MultipleResponseType<{
      responses: (PublicLogisticProviderResponseType & {
        logisticProvider: LogisticProviderProfileType & {
          auth: PublicAuthType;
        };
      })[];
    }>
  >({
    queryKey: [
      "logistic-provider-responses",
      deliveryRequestId,
      currentPage,
      currentSort,
      currentMinTotalPrice,
      currentMaxTotalPrice,
    ],
    queryFn: () =>
      getLogisticProviderResponses({
        token,
        deliveryRequestId,
        page: currentPage,
        sort: currentSort,
        minTotalPrice: currentMinTotalPrice,
        maxTotalPrice: currentMaxTotalPrice,
      }),
  });

  const acceptLogisticProviderResponseMutation = useMutation({
    mutationFn: acceptLogisticProviderResponse,
    onSuccess: ({ info }) => {
      toast.success(info.message || "Logistic provider accepted successfully");

      queryClient.invalidateQueries({ queryKey: ["delivery-request"] });
      queryClient.invalidateQueries({
        queryKey: ["logistic-provider-responses"],
      });
    },
    onError: (error) => {
      if (error instanceof AxiosError) {
        toast.error(
          error.response?.data.info?.message ||
            "Failed to accept logistic provider",
        );
      } else {
        toast.error("An unexpected error occurred");
      }
    },
  });

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(window.location.search);
    params.set("page", page.toString());
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    router.push(newUrl);
  };

  const responses = logisticProviderResponsesQuery?.data?.responses || [];

  return (
    <DataTable
      data={responses}
      columns={[
        {
          header: "Logistic Provider",
          cell: (response) => (
            <div className={cn("flex items-center gap-2")}>
              <Avatar className="size-10">
                <AvatarImage
                  src={`${process.env.NEXT_PUBLIC_FILE_URL}/${response.logisticProvider.pictureId}`}
                  alt={response.logisticProvider.name}
                  className={cn("object-cover")}
                />
                <AvatarFallback>
                  {response.logisticProvider.name
                    .split(" ")
                    .map((part) => part.charAt(0).toUpperCase())
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div className="flex flex-col text-wrap">
                <span className="text-sm font-medium">
                  {response.logisticProvider.name}
                </span>
                <span className="text-xs text-muted-foreground">
                  {response.logisticProvider.auth.email}
                </span>
              </div>
            </div>
          ),
        },
        {
          header: "Price",
          cell: (response) => <span>{formatPrice(response.price)}</span>,
        },
        {
          header: "Date",
          cell: (response) => <span>{formatDate(response.createdAt)}</span>,
        },
        {
          header: "",
          cell: (response) => (
            <Button
              variant="outline"
              size="icon"
              disabled={
                !!acceptedLogisticProviderId ||
                acceptLogisticProviderResponseMutation.isPending
              }
              onClick={() => {
                if (!acceptedLogisticProviderId) {
                  acceptLogisticProviderResponseMutation.mutate({
                    token,
                    responseId: response.id,
                  });
                }
              }}
            >
              {acceptLogisticProviderResponseMutation.isPending ? (
                <Loader2Icon className="size-4 animate-spin" />
              ) : (
                <CheckIcon className="size-4" />
              )}
              <span className="sr-only">Accept</span>
            </Button>
          ),
        },
      ]}
      currentPage={currentPage}
      meta={logisticProviderResponsesQuery?.meta}
      onPageChange={handlePageChange}
      itemName="responses"
      emptyState={{
        icon: ShoppingCartIcon,
        title: "No responses found",
        description: "No responses match your current filters.",
      }}
    />
  );
}
