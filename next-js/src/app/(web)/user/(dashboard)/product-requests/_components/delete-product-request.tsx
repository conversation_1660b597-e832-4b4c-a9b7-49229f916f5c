"use client";

import { useState } from "react";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import axios, { AxiosError } from "axios";
import { Loader2Icon, Trash2Icon } from "lucide-react";
import { toast } from "sonner";

import { But<PERSON> } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { useAuthContext } from "~/context/auth";
import { routes } from "~/lib/routes";
import { cn } from "~/lib/utils";

async function deleteProductRequest({
  token,
  id,
}: {
  token: string | null;
  id: string;
}) {
  const response = await axios.delete(routes.api.user.productRequests.url(id), {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return response.data;
}

export function DeleteProductRequest({ id }: { id: string }) {
  const queryClient = useQueryClient();
  const { token } = useAuthContext();
  const [isDeleteProductRequestOpen, setIsDeleteProductRequestOpen] =
    useState(false);

  const deleteProductRequestMutation = useMutation({
    mutationFn: deleteProductRequest,
    onSuccess: ({ info }) => {
      toast.success(info.message);
      setIsDeleteProductRequestOpen(false);
      queryClient.invalidateQueries({ queryKey: ["productRequests"] });
    },
    onError: (error) => {
      if (error instanceof AxiosError) {
        toast.error(error.response?.data.info.message);
      }
    },
  });

  return (
    <Dialog
      open={isDeleteProductRequestOpen}
      onOpenChange={setIsDeleteProductRequestOpen}
    >
      <DialogTrigger asChild>
        <Button variant="outline" size="icon" className={cn("size-8")}>
          <Trash2Icon className="size-4 text-red-500" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-sm">
        <DialogHeader>
          <DialogTitle>Delete Request</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this product request? This action
            cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            size="lg"
            className={cn("flex-1")}
            onClick={() => setIsDeleteProductRequestOpen(false)}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            size="lg"
            className={cn("flex-1")}
            disabled={deleteProductRequestMutation.isPending}
            onClick={() => deleteProductRequestMutation.mutate({ token, id })}
          >
            {deleteProductRequestMutation.isPending && (
              <Loader2Icon className={cn("mr-2 size-4 animate-spin")} />
            )}
            Delete
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
