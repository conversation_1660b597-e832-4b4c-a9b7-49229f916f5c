"use client";

import type { FormEvent } from "react";

import type {
  MultipleResponseType,
  ProductRequestType,
  PublicCategoryType,
  PublicProductType,
} from "~/lib/types";

import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";

import { useQuery } from "@tanstack/react-query";

import axios from "axios";
import { ClipboardList, ExternalLinkIcon } from "lucide-react";

import { PageLayout } from "~/components/common/page-layout";
import { SearchFilterBar } from "~/components/layout/search-filter-bar";
import { DataTable } from "~/components/table/data-table";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Badge } from "~/components/ui/badge";
import { Button, buttonVariants } from "~/components/ui/button";
import { useAuthContext } from "~/context/auth";
import { routes } from "~/lib/routes";
import { cn, formatPrice } from "~/lib/utils";
import { DeleteProductRequest } from "./_components/delete-product-request";
import { RequestProduct } from "./_components/request-product";

async function getProductRequests({
  token,
  page = 1,
  limit = 10,
  sort = "",
  name = "",
}: {
  token: string | null;
  page?: number;
  limit?: number;
  sort?: string;
  name?: string;
}) {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });

  if (sort) params.append("sort", sort);
  if (name) params.append("name", name);

  const url = `${routes.api.user.productRequests.url()}?${params.toString()}`;

  const response = await axios.get(url, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return response.data;
}

export default function ProductRequestsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { token, auth } = useAuthContext();
  const [queryTerm, setQueryTerm] = useState("");

  const currentPage = Number(searchParams.get("page") || "1");
  const currentName = searchParams.get("name") || "";
  const currentSort = searchParams.get("sort") || "";

  const {
    data: productRequestsQuery,
    isLoading: productRequestsQueryIsLoading,
    isError: productRequestsQueryIsError,
  } = useQuery<
    MultipleResponseType<{
      productRequests: (ProductRequestType & {
        category: PublicCategoryType;
        products: PublicProductType[];
      })[];
    }>
  >({
    queryKey: ["productRequests", currentPage, currentName, currentSort],
    queryFn: () =>
      getProductRequests({
        token,
        page: currentPage,
        name: currentName,
        sort: currentSort,
      }),
    enabled: !!auth && auth.role === "USER",
  });

  const handleSearch = (e: FormEvent) => {
    e.preventDefault();
    const params = new URLSearchParams(window.location.search);

    if (queryTerm) {
      params.set("name", queryTerm);
    } else {
      params.delete("name");
    }

    params.delete("page");

    const newUrl = `${window.location.pathname}${
      params.toString() ? `?${params.toString()}` : ""
    }`;
    router.push(newUrl);
  };

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    if (page > 1) {
      params.set("page", page.toString());
    } else {
      params.delete("page");
    }
    router.push(`${window.location.pathname}?${params.toString()}`);
  };

  if (!auth || auth.role !== "USER") {
    return (
      <PageLayout
        title="Access Denied"
        description="You need to be logged in as a user to view product requests."
        isError={true}
      >
        <div className="flex justify-center">
          <Button
            variant="default-gradient"
            onClick={() => router.push(routes.app.public.home.url())}
          >
            Go to Home
          </Button>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout
      title="Product Requests"
      description="View your product requests and submit new ones."
      actions={
        <div className="flex gap-2">
          <RequestProduct />
          {productRequestsQueryIsError && (
            <Button
              variant="default-gradient"
              onClick={() => window.location.reload()}
            >
              Retry
            </Button>
          )}
        </div>
      }
      isLoading={productRequestsQueryIsLoading}
      isError={productRequestsQueryIsError}
    >
      <SearchFilterBar
        queryTerm={queryTerm}
        setQueryTerm={setQueryTerm}
        handleSearch={handleSearch}
        placeholder="Search by name..."
      />

      <DataTable
        data={productRequestsQuery?.data?.productRequests || []}
        columns={[
          {
            header: "Request",
            cell: (request) => (
              <div
                className={cn("flex items-center gap-2 w-full overflow-hidden")}
              >
                <Avatar className="size-10 rounded-md flex-shrink-0">
                  {request.pictureIds?.[0] ? (
                    <AvatarImage
                      src={`${process.env.NEXT_PUBLIC_FILE_URL}/${request.pictureIds[0]}`}
                      alt={request.name}
                      className={cn("object-cover")}
                    />
                  ) : null}
                  <AvatarFallback className={cn("rounded-md")}>
                    {request.name
                      .split(" ")
                      .map((part) => part.charAt(0).toUpperCase())
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-col min-w-0 flex-1 overflow-hidden">
                  <span className="text-sm font-medium truncate">
                    {request.name}
                  </span>
                  <span className="text-xs text-muted-foreground line-clamp-2">
                    {request.description}
                  </span>
                </div>
              </div>
            ),
          },
          {
            header: "Category",
            cell: (request) => (
              <Badge variant="outline" className="truncate max-w-full">
                {request.category.name}
              </Badge>
            ),
          },
          {
            header: "Quantity",
            cell: (request) => <span>{request.quantity}</span>,
          },
          {
            header: "Price",
            cell: (request) => <span>{formatPrice(request.price)}</span>,
          },
          {
            header: "Products",
            cell: (request) => <span>{request.products.length}</span>,
          },
          {
            header: "",
            cell: (request) => (
              <div className={cn("space-x-2")}>
                <Link
                  href={`${routes.app.public.products.url()}?productRequestId=${
                    request.id
                  }`}
                  className={cn(
                    buttonVariants({
                      variant: "outline",
                      size: "icon",
                      className: "size-8",
                    }),
                  )}
                >
                  <ExternalLinkIcon className="size-4" />
                </Link>
                <DeleteProductRequest id={request.id} />
              </div>
            ),
          },
        ]}
        currentPage={currentPage}
        meta={productRequestsQuery?.meta}
        onPageChange={handlePageChange}
        itemName="requests"
        emptyState={{
          icon: ClipboardList,
          title: "No requests found",
          description: "You haven't made any requests yet.",
          action: {
            label: "Request Product",
            onClick: () => router.push(routes.app.public.products.url()),
          },
        }}
      />
    </PageLayout>
  );
}
