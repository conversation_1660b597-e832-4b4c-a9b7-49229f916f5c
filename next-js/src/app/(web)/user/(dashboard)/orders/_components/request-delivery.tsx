"use client";

import { useState } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";

import axios, { AxiosError } from "axios";
import { Loader2Icon } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import * as zod from "zod";

import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { useAuthContext } from "~/context/auth";
import { routes } from "~/lib/routes";
import type {
  PublicDeliveryRequestType,
  SingleResponseType,
} from "~/lib/types";
import { cn } from "~/lib/utils";

const DeliveryRequestFormSchema = zod.object({
  price: zod.coerce
    .number({
      required_error: "Price is required",
      invalid_type_error: "Price must be a number",
    })
    .min(1, {
      message: "Price must be at least 1",
    }),
});

async function createDeliveryRequest({
  token,
  orderId,
  data,
}: {
  token: string | null;
  orderId: string;
  data: zod.infer<typeof DeliveryRequestFormSchema>;
}) {
  const response = await axios.post<
    SingleResponseType<{
      deliveryRequest: PublicDeliveryRequestType;
    }>
  >(routes.api.user.deliveryRequests.url(orderId), data, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return response.data;
}

interface RequestDeliveryButtonProps {
  orderId: string;
  disabled?: boolean;
}

export function RequestDeliveryButton({
  orderId,
  disabled = false,
}: RequestDeliveryButtonProps) {
  const queryClient = useQueryClient();

  const { token } = useAuthContext();

  const [isOpen, setIsOpen] = useState(false);

  const form = useForm<zod.infer<typeof DeliveryRequestFormSchema>>({
    resolver: zodResolver(DeliveryRequestFormSchema),
    defaultValues: {
      price: 0,
    },
  });

  const createDeliveryRequestMutation = useMutation({
    mutationFn: createDeliveryRequest,
    onSuccess: ({ info }) => {
      toast.success(info.message);
      setIsOpen(false);
      queryClient.invalidateQueries({ queryKey: ["orders"] });
    },
    onError: (error) => {
      if (error instanceof AxiosError) {
        toast.error(
          error.response?.data.info.message ||
            "Failed to create delivery request",
        );
      } else {
        toast.error("An unexpected error occurred");
      }
    },
  });

  const onSubmit = (data: zod.infer<typeof DeliveryRequestFormSchema>) => {
    createDeliveryRequestMutation.mutate({ token, orderId, data });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" disabled={disabled}>
          Request Delivery
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Request Delivery</DialogTitle>
          <DialogDescription>
            Enter the price you're willing to pay for delivery. Logistic
            providers will respond with their offers.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className={cn("space-y-6")}
          >
            <FormField
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Delivery Price</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="Enter price" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              variant="default-gradient"
              size="lg"
              className={cn("w-full")}
              type="submit"
              disabled={createDeliveryRequestMutation.isPending}
            >
              {createDeliveryRequestMutation.isPending && (
                <Loader2Icon className={cn("mr-2 size-4 animate-spin")} />
              )}
              <span>Submit Request</span>
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
