"use client";

import type { PublicConversation } from "~/lib/types";

import { use } from "react";

import { Conversation } from "../_components/conversation";
import { mockConversations } from "../_components/conversations-list";

export default function ConversationPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const resolvedParams = use(params);
  const conversation = mockConversations.find(
    (c: PublicConversation) => c.id === resolvedParams.id,
  );

  if (!conversation) {
    return <div>Conversation not found</div>;
  }

  const mockMessages = [
    {
      id: "msg1",
      content: "Hello there!",
      sender: conversation.members[0],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: "msg2",
      content: "Hi! How are you?",
      sender: conversation.members[1],
      createdAt: new Date(Date.now() - 1000 * 60 * 2),
      updatedAt: new Date(Date.now() - 1000 * 60 * 2),
    },
    {
      id: "msg3",
      content: "I am good, thanks for asking!",
      sender: conversation.members[0],
      createdAt: new Date(Date.now() - 1000 * 60 * 1),
      updatedAt: new Date(Date.now() - 1000 * 60 * 1),
    },
    {
      id: "msg4",
      content:
        "This is a longer message to see how the bubble handles it. It should wrap nicely and not break the layout. We can even add more text to make it even longer and test the wrapping capabilities of the message bubble component.",
      sender: conversation.members[1],
      createdAt: new Date(Date.now() - 1000 * 30),
      updatedAt: new Date(Date.now() - 1000 * 30),
    },
    {
      id: "msg5",
      content: "This is a follow-up message.",
      sender: conversation.members[0],
      createdAt: new Date(Date.now() - 1000 * 10),
      updatedAt: new Date(Date.now() - 1000 * 10),
    },
    {
      id: "msg6",
      content: "Thanks for the update!",
      sender: conversation.members[1],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: "msg7",
      content: "No problem! Let me know if you need anything else.",
      sender: conversation.members[0],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: "msg8",
      content: "Sure! I will keep you posted.",
      sender: conversation.members[1],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: "msg9",
      content: "Thanks for the update!",
      sender: conversation.members[1],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: "msg10",
      content: "You're welcome! Talk to you later.",
      sender: conversation.members[0],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  const currentUserId = conversation.members[0].id;

  return (
    <Conversation
      conversation={conversation}
      messages={mockMessages}
      currentUserId={currentUserId}
    />
  );
}
