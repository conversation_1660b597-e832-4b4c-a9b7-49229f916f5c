"use client";

import type { PublicConversation } from "~/lib/types";

import Link from "next/link";

import { routes } from "~/lib/routes";
import { ConversationEndedBy, ConversationType } from "~/lib/types";
import { cn } from "~/lib/utils";
import { ConversationsListItem } from "./conversations-list-item";
import { ConversationsSearch } from "./conversations-search";

export const mockConversations: PublicConversation[] = [
  {
    id: "1",
    referenceId: "1",
    endedBy: ConversationEndedBy.ADMIN,
    type: ConversationType.VENDOR,
    members: [
      {
        id: "1",
        name: "<PERSON>",
        pictureId: "1",
      },
      {
        id: "2",
        name: "<PERSON>",
        pictureId: "2",
      },
    ],
    createdAt: new Date("2023-10-01T12:00:00Z"),
    updatedAt: new Date("2023-10-01T12:00:00Z"),
  },
  {
    id: "2",
    referenceId: "2",
    endedBy: ConversationEndedBy.SYSTEM,
    type: ConversationType.VENDOR,
    members: [
      {
        id: "3",
        name: "<PERSON>",
        pictureId: "3",
      },
      {
        id: "4",
        name: "<PERSON>",
        pictureId: "4",
      },
    ],
    createdAt: new Date("2023-10-01T12:00:00Z"),
    updatedAt: new Date("2023-10-01T12:00:00Z"),
  },
  {
    id: "3",
    referenceId: "3",
    type: ConversationType.VENDOR,
    members: [
      {
        id: "5",
        name: "Charlie Davis",
        pictureId: "5",
      },
      {
        id: "6",
        name: "Dana Lee",
        pictureId: "6",
      },
    ],
    createdAt: new Date("2023-10-01T12:00:00Z"),
    updatedAt: new Date("2023-10-01T12:00:00Z"),
  },
  {
    id: "4",
    referenceId: "4",
    type: ConversationType.LOGISTIC,
    members: [
      {
        id: "7",
        name: "Eve Adams",
        pictureId: "7",
      },
      {
        id: "8",
        name: "Frank Clark",
        pictureId: "8",
      },
    ],
    createdAt: new Date("2023-10-01T12:00:00Z"),
    updatedAt: new Date("2023-10-01T12:00:00Z"),
  },
  {
    id: "5",
    referenceId: "5",
    type: ConversationType.LOGISTIC,
    members: [
      {
        id: "9",
        name: "Grace Miller",
        pictureId: "9",
      },
      {
        id: "10",
        name: "Henry Wilson",
        pictureId: "10",
      },
    ],
    createdAt: new Date("2023-10-01T12:00:00Z"),
    updatedAt: new Date("2023-10-01T12:00:00Z"),
  },
  {
    id: "6",
    referenceId: "6",
    endedBy: ConversationEndedBy.SYSTEM,
    type: ConversationType.VENDOR,
    members: [
      {
        id: "11",
        name: "Ivy Moore",
        pictureId: "11",
      },
      {
        id: "12",
        name: "Jack Taylor",
        pictureId: "12",
      },
    ],
    createdAt: new Date("2023-10-01T12:00:00Z"),
    updatedAt: new Date("2023-10-01T12:00:00Z"),
  },
  {
    id: "7",
    referenceId: "7",
    type: ConversationType.VENDOR,
    members: [
      {
        id: "13",
        name: "Liam Johnson",
        pictureId: "13",
      },
      {
        id: "14",
        name: "Mia Williams",
        pictureId: "14",
      },
    ],
    createdAt: new Date("2023-10-01T12:00:00Z"),
    updatedAt: new Date("2023-10-01T12:00:00Z"),
  },
];

export function ConversationsList() {
  return (
    <div className={cn("flex flex-col h-full")}>
      <ConversationsSearch onSearch={() => {}} />
      <div className={cn("flex-grow overflow-y-auto space-y-2 p-2")}>
        {mockConversations.map((conversation) => (
          <Link
            key={conversation.id}
            href={routes.app.user.conversations.url(conversation.id)}
            className={cn("block")}
          >
            <ConversationsListItem conversation={conversation} />
          </Link>
        ))}
      </div>
    </div>
  );
}
