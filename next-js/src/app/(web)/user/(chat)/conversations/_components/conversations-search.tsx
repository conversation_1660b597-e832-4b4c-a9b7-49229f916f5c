"use client";

import { Search } from "lucide-react";

import { Input } from "~/components/ui/input";
import { cn } from "~/lib/utils";

interface ConversationsSearchProps {
  onSearch: (query: string) => void;
}

export function ConversationsSearch({ onSearch }: ConversationsSearchProps) {
  return (
    <div className={cn("p-4 border-b border-gray-200 dark:border-gray-700")}>
      <div className={cn("relative")}>
        <Search
          className={cn(
            "absolute left-3 top-1/2 -translate-y-1/2 size-4 text-gray-500 dark:text-gray-400",
          )}
        />
        <Input
          type="search"
          placeholder="Search conversations..."
          className={cn("pl-10 w-full")}
          onChange={(e) => onSearch(e.target.value)}
        />
      </div>
    </div>
  );
}
