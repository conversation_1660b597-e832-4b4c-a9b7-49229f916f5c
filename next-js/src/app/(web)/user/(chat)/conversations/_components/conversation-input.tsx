"use client";

import { useState } from "react";

import { SendIcon, XCircle } from "lucide-react";

import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { cn } from "~/lib/utils";

interface ConversationInputProps {
  onSendMessage: (content: string) => void;
  isDisabled?: boolean;
  disabledReason?: string;
}

export function ConversationInput({
  onSendMessage,
  isDisabled = false,
  disabledReason = "This conversation has been ended",
}: ConversationInputProps) {
  const [message, setMessage] = useState("");

  const handleSend = () => {
    if (message.trim() && !isDisabled) {
      onSendMessage(message.trim());
      setMessage("");
    }
  };

  if (isDisabled) {
    return (
      <div
        className={cn(
          "p-4 border-t border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950/20",
        )}
      >
        <div
          className={cn(
            "flex items-center justify-center space-x-2 text-red-600 dark:text-red-400",
          )}
        >
          <XCircle className={cn("size-5")} />
          <span className={cn("text-sm font-medium")}>{disabledReason}</span>
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800",
      )}
    >
      <div className={cn("flex items-center space-x-2")}>
        <Input
          type="text"
          placeholder="Type a message..."
          className={cn(
            "flex-1 rounded-full px-4 py-2 border-gray-300 dark:border-gray-600 focus:ring-primary-500 focus:border-primary-500",
          )}
          value={message}
          onChange={(event) => setMessage(event.target.value)}
          onKeyDown={(event) => {
            if (event.key === "Enter" && !event.shiftKey) {
              event.preventDefault();
              handleSend();
            }
          }}
        />
        <Button
          type="button"
          variant="default-gradient"
          size="icon"
          className={cn("rounded-full")}
          disabled={!message.trim()}
          onClick={handleSend}
        >
          <SendIcon className={cn("size-5")} />
        </Button>
      </div>
    </div>
  );
}
