"use client";

import { MessageSquare } from "lucide-react";

import { cn } from "~/lib/utils";

export function ConversationNoSelected() {
  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center h-full text-center",
      )}
    >
      <MessageSquare
        className={cn("w-16 h-16 text-gray-400 dark:text-gray-500 mb-4")}
      />
      <h2
        className={cn("text-xl font-semibold text-gray-700 dark:text-gray-300")}
      >
        No conversation selected
      </h2>
      <p className={cn("text-gray-500 dark:text-gray-400")}>
        Select a conversation from the list to start chatting.
      </p>
    </div>
  );
}
