"use client";

import type { PublicConversation } from "~/lib/types";

import { useParams } from "next/navigation";

import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Badge } from "~/components/ui/badge";
import { ConversationEndedBy } from "~/lib/types";
import { cn } from "~/lib/utils";

interface ConversationsListItemProps {
  conversation: PublicConversation;
}

export function ConversationsListItem({
  conversation,
}: ConversationsListItemProps) {
  const params = useParams();
  const isActive = params.id === conversation.id;
  const isEnded = !!conversation.endedBy;

  const otherMember = conversation.members[1] ?? conversation.members[0];

  const getEndedBadgeText = () => {
    if (conversation.endedBy === ConversationEndedBy.SYSTEM) {
      return "Ended by System";
    }
    if (conversation.endedBy === ConversationEndedBy.ADMIN) {
      return "Ended by Admin";
    }
    return "Ended";
  };

  return (
    <div
      className={cn(
        "flex items-center p-3 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg cursor-pointer transition-colors",
        isActive && "bg-gray-100 dark:bg-gray-800",
        isEnded && "opacity-75",
      )}
    >
      <Avatar className={cn("size-10 mr-3")}>
        <AvatarImage
          src={`https://avatar.vercel.sh/${otherMember.pictureId}.png`}
          alt={otherMember.name}
        />
        <AvatarFallback>{otherMember.name.charAt(0)}</AvatarFallback>
      </Avatar>
      <div className={cn("flex-1 min-w-0")}>
        <div className={cn("flex items-center justify-between mb-1")}>
          <p
            className={cn(
              "text-sm font-medium text-gray-900 dark:text-white truncate",
              isEnded && "text-gray-500 dark:text-gray-400",
            )}
          >
            {otherMember.name}
          </p>
        </div>
        <div className={cn("flex items-center justify-between")}>
          <p
            className={cn("text-xs text-gray-500 dark:text-gray-400 truncate")}
          >
            {conversation.type}
          </p>
          {isEnded && (
            <Badge
              variant="destructive"
              className={cn("text-xs ml-2 flex-shrink-0")}
            >
              {getEndedBadgeText()}
            </Badge>
          )}
        </div>
      </div>
    </div>
  );
}
