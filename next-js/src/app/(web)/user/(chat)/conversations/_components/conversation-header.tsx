"use client";

import type { PublicConversation } from "~/lib/types";

import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Badge } from "~/components/ui/badge";
import { ConversationEndedBy } from "~/lib/types";
import { cn } from "~/lib/utils";

interface ConversationHeaderProps {
  conversation: PublicConversation;
}

export function ConversationHeader({ conversation }: ConversationHeaderProps) {
  const otherMember = conversation.members[1] ?? conversation.members[0];
  const isEnded = !!conversation.endedBy;

  const getEndedBadgeText = () => {
    if (conversation.endedBy === ConversationEndedBy.SYSTEM) {
      return "Ended by System";
    }
    if (conversation.endedBy === ConversationEndedBy.ADMIN) {
      return "Ended by Admin";
    }
    return "Ended";
  };

  return (
    <div
      className={cn(
        "flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800",
        isEnded &&
          "bg-red-50 dark:bg-red-950/20 border-red-200 dark:border-red-800",
      )}
    >
      <div className={cn("flex items-center space-x-3")}>
        <Avatar className={cn("size-10")}>
          <AvatarImage
            src={`https://avatar.vercel.sh/${otherMember.pictureId}.png`}
            alt={otherMember.name}
          />
          <AvatarFallback>{otherMember.name.charAt(0)}</AvatarFallback>
        </Avatar>
        <div>
          <p
            className={cn(
              "text-sm font-medium text-gray-900 dark:text-white",
              isEnded && "text-gray-500 dark:text-gray-400",
            )}
          >
            {otherMember.name}
          </p>
        </div>
      </div>
      <div className={cn("flex items-center space-x-2")}>
        {isEnded && (
          <Badge variant="destructive" className={cn("text-xs")}>
            {getEndedBadgeText()}
          </Badge>
        )}
      </div>
    </div>
  );
}
