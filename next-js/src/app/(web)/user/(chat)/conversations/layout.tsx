"use client";

import type { PropsWithChildren } from "react";

import { useParams } from "next/navigation";
import { useIsMobile } from "~/hooks/use-mobile";
import { cn } from "~/lib/utils";
import { ConversationsList } from "./_components/conversations-list";

export default function ConversationsLayout({ children }: PropsWithChildren) {
  const params = useParams();
  const isMobile = useIsMobile();

  const hasSelectedConversation = !!params.id;

  if (isMobile) {
    return (
      <div className={cn("h-full")}>
        {hasSelectedConversation ? children : <ConversationsList />}
      </div>
    );
  }

  return (
    <div
      className={cn(
        "grid grid-cols-1 md:grid-cols-3 gap-4 h-[calc(100vh-200px)]",
      )}
    >
      <div
        className={cn(
          "col-span-1 md:col-span-1 h-full overflow-y-auto border-r border-gray-200 dark:border-gray-700",
        )}
      >
        <ConversationsList />
      </div>
      <div className={cn("col-span-1 md:col-span-2 h-full overflow-y-auto")}>
        {children}
      </div>
    </div>
  );
}
