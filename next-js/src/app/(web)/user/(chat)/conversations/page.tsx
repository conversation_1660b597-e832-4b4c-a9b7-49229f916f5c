"use client";

import { useRouter, useSearchParams } from "next/navigation";

import { useCallback, useEffect } from "react";
import { useAuthContext } from "~/context/auth";
import { useSocket } from "~/hooks/use-socket";
import { events } from "~/lib/events";
import { routes } from "~/lib/routes";
import { ConversationNoSelected } from "./_components/conversation-no-selected";

export default function ConversationsPage() {
  const searchParams = useSearchParams();

  const router = useRouter();

  const { token } = useAuthContext();

  const { socket } = useSocket(token);

  const joinConversation = useCallback(() => {
    const type = searchParams.get("type");
    const profileId = searchParams.get("profileId");
    const referenceId = searchParams.get("referenceId");

    if (type && profileId && referenceId && socket?.connected) {
      socket.emit(
        events.conversations.join,
        {
          type,
          profileId,
          referenceId,
        },
        (body: { success: boolean; data?: unknown; error?: string }) => {
          if (body.success && !body.error && body.data) {
            router.push(
              // @ts-ignore
              routes.app.user.conversations.url(body.data.id),
            );
          }
        },
      );
    }
  }, [socket, searchParams, router]);

  const handleReceiveConversations = useCallback(() => {
    console.log("Received conversations");
  }, []);

  useEffect(() => {
    if (!socket) {
      return;
    }

    if (socket.connected) {
      joinConversation();
    }

    socket.on(events.socket.connect, joinConversation);
    socket.on(events.conversations.receive, handleReceiveConversations);

    return () => {
      socket.off(events.socket.connect, joinConversation);
      socket.off(events.conversations.receive, handleReceiveConversations);
    };
  }, [socket, joinConversation, handleReceiveConversations]);

  return <ConversationNoSelected />;
}
