"use client";

import Image from "next/image";
import Link from "next/link";

import {
  ArrowDown,
  ArrowRight,
  CreditCard,
  ShoppingCart,
  Truck,
  User,
  UserCheck,
} from "lucide-react";

import { assets } from "~/assets";
// import { Badge } from "~/components/ui/badge";
import { Button, buttonVariants } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { domine } from "~/lib/fonts";
import { routes } from "~/lib/routes";
import { cn } from "~/lib/utils";

export default function HomePage() {
  const handleLearnMore = () => {
    const workSection = document.getElementById("work");
    if (workSection) {
      const rect = workSection.getBoundingClientRect();
      const scrollTop =
        window.pageYOffset || document.documentElement.scrollTop;
      const targetPosition = rect.top + scrollTop - window.innerHeight * 0.25;

      window.scrollTo({
        top: targetPosition,
        behavior: "smooth",
      });
    }
  };

  return (
    <>
      <section
        className={cn(
          "py-14 px-7 md:px-24 min-h-[calc(100svh_-_5rem)] md:min-h-[calc(100svh_-_12rem)] flex flex-col-reverse lg:flex-row gap-12 justify-center items-center",
        )}
      >
        <div className={cn("flex-1 space-y-12")}>
          <div className={cn("space-y-4")}>
            {/* <Badge
              variant="outline-gradient"
              className={cn("uppercase font-light")}
            >
              Who We Are
            </Badge> */}
            <h2
              className={cn(
                "text-black/80 text-4xl lg:text-6xl font-medium",
                domine.className,
              )}
            >
              Cost-effective solutions for the construction sector.
            </h2>
            <p
              className={cn(
                "text-muted-foreground text-xl md:text-lg lg:text-xl font-medium",
              )}
            >
              Save costs. Build sustainably. Buy or sell used or surplus
              construction materials.
            </p>
          </div>
          <div className={cn("flex items-center gap-4")}>
            <Button onClick={handleLearnMore} variant="secondary" size="xl">
              Learn more
            </Button>
            <Link
              href={routes.app.public.products.url()}
              className={cn(
                buttonVariants({ variant: "default-gradient", size: "xl" }),
              )}
            >
              Get started
            </Link>
          </div>
        </div>
        <div className={cn("flex-1")}>
          <Image
            src={assets.pictures.homePage.heroSectionBanner.src}
            alt={assets.pictures.homePage.heroSectionBanner.alt}
          />
        </div>
      </section>
      <section
        id="work"
        className={cn(
          "py-14 px-7 md:px-24 min-h-[calc(100svh_-_5rem)] md:min-h-[calc(100svh_-_12rem)] flex flex-col-reverse lg:flex-row-reverse gap-12 justify-center",
        )}
      >
        <div className={cn("flex-1 space-y-12")}>
          <div className={cn("space-y-4")}>
            {/* <Badge
              variant="outline-gradient"
              className={cn("uppercase font-light")}
            >
              Why Choose Us
            </Badge> */}
            <h2
              className={cn(
                "text-black/80 text-4xl lg:text-6xl font-medium",
                domine.className,
              )}
            >
              We Help You Build Smarter!
            </h2>
            <p
              className={cn(
                "text-muted-foreground text-xl md:text-lg lg:text-xl font-medium",
              )}
            >
              Our vetted vendors simplifies sourcing of used materials, helping
              you lower costs and build greener projects — without compromising
              quality.
            </p>
          </div>
          <div className={cn("flex items-center gap-4")}>
            <Link
              href={routes.app.public.vendors.url()}
              className={cn(
                buttonVariants({ variant: "default-gradient", size: "xl" }),
              )}
            >
              Browse vendors
            </Link>
          </div>
        </div>
        <div className={cn("flex-1")}>
          <Image
            src={assets.pictures.homePage.workSectionBanner.src}
            alt={assets.pictures.homePage.workSectionBanner.alt}
          />
        </div>
      </section>
      <section
        className={cn(
          "py-14 px-7 md:px-24 min-h-[calc(100svh_-_5rem)] md:min-h-[calc(100svh_-_12rem)]",
        )}
      >
        <div className={cn("flex-1 flex flex-col gap-12")}>
          <div
            className={cn(
              "flex flex-col items-center gap-4 text-center max-w-3xl mx-auto",
            )}
          >
            {/* <Badge
              variant="outline-gradient"
              className={cn("uppercase font-light")}
            >
              Testimonials
            </Badge> */}
            <h2
              className={cn(
                "text-black/80 text-4xl lg:text-6xl font-medium",
                domine.className,
              )}
            >
              Ready to join the Movement? Here's How.
            </h2>
            <p
              className={cn(
                "text-muted-foreground text-xl md:text-lg lg:text-xl font-medium",
              )}
            >
              Whether you're a builder, architect, or sustainability advocate,
              EcoBuiltConnect has something for you. Join us in transforming the
              construction industry!
            </p>
          </div>
          <div
            className={cn(
              "flex flex-col md:flex-row justify-center items-center gap-4",
            )}
          >
            <article
              className={cn(
                "flex flex-col justify-center items-center gap-4 p-2",
              )}
            >
              <div
                className={cn(
                  "size-20 bg-gradient-to-r from-sky-600 to-sky-500 rounded-full flex items-center justify-center",
                )}
              >
                <ShoppingCart className={cn("size-8 text-white")} />
              </div>
              <div className={cn("text-center")}>
                <h3 className={cn("text-lg font-medium")}>Step 1</h3>
                <p className={cn("text-muted-foreground max-w-64")}>
                  Browse our marketplace and select the construction materials
                  you need
                </p>
              </div>
            </article>

            {/* Mobile Arrow */}
            <ArrowDown
              className={cn("block md:hidden text-muted-foreground size-6")}
            />

            {/* Desktop Arrow */}
            <ArrowRight
              className={cn("hidden md:block text-muted-foreground size-6")}
            />

            <article
              className={cn(
                "flex flex-col justify-center items-center gap-4 p-2",
              )}
            >
              <div
                className={cn(
                  "size-20 bg-gradient-to-r from-sky-600 to-sky-500 rounded-full flex items-center justify-center",
                )}
              >
                <CreditCard className={cn("size-8 text-white")} />
              </div>
              <div className={cn("text-center")}>
                <h3 className={cn("text-lg font-medium")}>Step 2</h3>
                <p className={cn("text-muted-foreground max-w-64")}>
                  Add items to your cart and proceed to checkout
                </p>
              </div>
            </article>

            {/* Mobile Arrow */}
            <ArrowDown
              className={cn("block md:hidden text-muted-foreground size-6")}
            />

            {/* Desktop Arrow */}
            <ArrowRight
              className={cn("hidden md:block text-muted-foreground size-6")}
            />

            <article
              className={cn(
                "flex flex-col justify-center items-center gap-4 p-2",
              )}
            >
              <div
                className={cn(
                  "size-20 bg-gradient-to-r from-sky-600 to-sky-500 rounded-full flex items-center justify-center",
                )}
              >
                <User className={cn("size-8 text-white")} />
              </div>
              <div className={cn("text-center")}>
                <h3 className={cn("text-lg font-medium")}>Step 3</h3>
                <p className={cn("text-muted-foreground max-w-64")}>
                  Sign in to your account and verify your credentials with OTP
                </p>
              </div>
            </article>

            {/* Mobile Arrow */}
            <ArrowDown
              className={cn("block md:hidden text-muted-foreground size-6")}
            />

            {/* Desktop Arrow */}
            <ArrowRight
              className={cn("hidden md:block text-muted-foreground size-6")}
            />

            <article
              className={cn(
                "flex flex-col justify-center items-center gap-4 p-2",
              )}
            >
              <div
                className={cn(
                  "size-20 bg-gradient-to-r from-sky-600 to-sky-500 rounded-full flex items-center justify-center",
                )}
              >
                <Truck className={cn("size-8 text-white")} />
              </div>
              <div className={cn("text-center")}>
                <h3 className={cn("text-lg font-medium")}>Step 4</h3>
                <p className={cn("text-muted-foreground max-w-64")}>
                  Choose your delivery method, process payment, and you're done!
                </p>
              </div>
            </article>
          </div>
        </div>
      </section>
      <section
        className={cn(
          "py-14 px-7 md:px-24 min-h-[calc(100svh_-_5rem)] md:min-h-[calc(100svh_-_12rem)] flex justify-center items-center",
        )}
      >
        <div
          className={cn("flex-1 flex flex-col items-center gap-12 max-w-3xl")}
        >
          <div className={cn("flex flex-col items-center gap-4 text-center")}>
            {/* <Badge
              variant="outline-gradient"
              className={cn("uppercase font-light")}
            >
              Testimonials
            </Badge> */}
            <h2
              className={cn(
                "text-black/80 text-4xl lg:text-6xl font-medium",
                domine.className,
              )}
            >
              Hear from Our Satisfied Customers!
            </h2>
            <p
              className={cn(
                "text-muted-foreground text-xl md:text-lg lg:text-xl font-medium",
              )}
            >
              See what builders, architects, and sustainability champions are
              saying about EcoBuiltConnect!
            </p>
          </div>
          <div
            className={cn(
              "flex flex-col md:flex-row justify-center items-center gap-4 flex-wrap",
            )}
          >
            <Card className={cn("py-2")}>
              <CardContent className={cn("flex gap-4 px-2")}>
                <div className={cn("size-20")}>
                  <Image
                    src={assets.pictures.testimonials.jackReul.src}
                    alt={assets.pictures.testimonials.jackReul.alt}
                    className={cn("object-cover rounded-full")}
                  />
                </div>
                <div>
                  <h3 className={cn("text-lg font-medium")}>Jack Reul</h3>
                  <p className={cn("text-muted-foreground max-w-64")}>
                    "Finally, a platform that makes finding quality recycled
                    construction materials easy and reliable."
                  </p>
                </div>
              </CardContent>
            </Card>
            <Card className={cn("py-2")}>
              <CardContent className={cn("flex gap-4 px-2")}>
                <div className={cn("size-20")}>
                  <Image
                    src={assets.pictures.testimonials.jessyIris.src}
                    alt={assets.pictures.testimonials.jessyIris.alt}
                    className={cn("object-cover rounded-full")}
                  />
                </div>
                <div>
                  <h3 className={cn("text-lg font-medium")}>Jessy Iris</h3>
                  <p className={cn("text-muted-foreground max-w-64")}>
                    "EcoBuiltConnect helped us cut material costs by 20% while
                    staying eco-friendly — a game changer!"
                  </p>
                </div>
              </CardContent>
            </Card>
            <Card className={cn("py-2")}>
              <CardContent className={cn("flex gap-4 px-2")}>
                <div className={cn("size-20")}>
                  <Image
                    src={assets.pictures.testimonials.markKeller.src}
                    alt={assets.pictures.testimonials.markKeller.alt}
                    className={cn("object-cover rounded-full")}
                  />
                </div>
                <div>
                  <h3 className={cn("text-lg font-medium")}>Mark Keller</h3>
                  <p className={cn("text-muted-foreground max-w-64")}>
                    "EcoBuiltConnect bridges the gap between sustainability and
                    affordability!"
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </>
  );
}
