import Image from "next/image";
import Link from "next/link";

import {
  SiFacebook,
  SiInstagram,
  SiX,
  SiYoutube,
} from "@icons-pack/react-simple-icons";
import axios from "axios";
import { MailIcon, MapPinIcon, PhoneIcon } from "lucide-react";

import { assets } from "~/assets";
import { routes } from "~/lib/routes";
import { cn } from "~/lib/utils";

type ListingItem = {
  label: string;
  url: string;
};

type Category = {
  id: string;
  name: string;
};

const socialLinkItems = [
  {
    icon: <SiFacebook size={24} />,
    url: "https://www.facebook.com",
  },
  {
    icon: <SiInstagram size={24} />,
    url: "https://www.instagram.com",
  },
  {
    icon: <SiYoutube size={24} />,
    url: "https://www.youtube.com",
  },
  {
    icon: <SiX size={24} />,
    url: "https://www.x.com",
  },
];

async function getTopCategories(): Promise<ListingItem[]> {
  try {
    const response = await axios.get(routes.api.public.categories.url());
    const categories: Category[] = response.data?.data?.categories || [];

    // Return top 5 categories
    return categories.slice(0, 5).map((category: Category) => ({
      label: category.name,
      url: `${routes.app.public.products.url()}?categoryId=${category.id}`,
    }));
  } catch (error) {
    console.error("Failed to fetch categories:", error);
    // Fallback to static data if API fails
    return [
      {
        label: "Residential Building Supplies",
        url: `${routes.app.public.products.url()}?categoryId=residential`,
      },
      {
        label: "Commercial Building Supplies",
        url: `${routes.app.public.products.url()}?categoryId=commercial`,
      },
      {
        label: "Interior Essentials & Appliances",
        url: `${routes.app.public.products.url()}?categoryId=interior`,
      },
      {
        label: "Home Systems & HVAC",
        url: `${routes.app.public.products.url()}?categoryId=hvac`,
      },
    ];
  }
}

const quickLinkItems = [
  {
    label: routes.app.public.home.label,
    url: routes.app.public.home.url(),
  },
  {
    label: routes.app.public.products.label,
    url: routes.app.public.products.url(),
  },
  {
    label: routes.app.public.vendors.label,
    url: routes.app.public.vendors.url(),
  },
  {
    label: routes.app.public.community.label,
    url: routes.app.public.community.url(),
  },
  {
    label: routes.app.public.contact.label,
    url: routes.app.public.contact.url(),
  },
];

const contactItems = [
  {
    icon: <PhoneIcon size={18} />,
    label: "+27 68 105 3549",
    url: "tel:+27681053549",
  },
  {
    icon: <MailIcon size={18} />,
    label: "<EMAIL>",
    url: "mailto:<EMAIL>",
  },
  {
    icon: <MapPinIcon size={20} />,
    label: "Goodwood, Cape Town",
    url: "https://www.google.com/maps/place/Goodwood,+Cape+Town",
  },
];

export async function RootFooter() {
  const listingsItems = await getTopCategories();

  return (
    <footer>
      <div className={cn("py-16 px-4 bg-secondary/50")}>
        <div
          className={cn(
            "flex flex-col md:flex-row gap-8 md:gap-24 mx-auto max-w-7xl",
          )}
        >
          <div className={cn("flex-3 flex flex-col items-center gap-4")}>
            <Image
              priority
              src={assets.pictures.app.logoTagLine.src}
              alt={assets.pictures.app.logoTagLine.alt}
              sizes="(max-width: 768px) 192px, 192px"
              className={cn("mt-1 ml-1 w-48 object-cover")}
            />
            <p className={cn("text-gray-600 text-sm text-center")}>
              Connect with trusted vendors and contractors to reduce waste, cut
              costs and contribute to a greener future.
            </p>
            <ul className={cn("flex items-center gap-6 text-gray-600")}>
              {socialLinkItems.map(({ url, icon }) => (
                <li key={url}>
                  <Link href={url} className={cn("hover:text-sky-600")}>
                    <span>{icon}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div className={cn("hidden md:block flex-2 space-y-6")}>
            <h2 className={cn("text-xl")}>Our Listings</h2>
            <ul className={cn("space-y-3 text-gray-600")}>
              {listingsItems.map(({ url, label }: ListingItem) => (
                <li key={url}>
                  <Link href={url} className={cn("hover:text-sky-600")}>
                    {label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div className={cn("hidden md:block  flex-1 space-y-6")}>
            <h2 className={cn("text-xl")}>Quick Links</h2>
            <ul className={cn("space-y-3 text-gray-600")}>
              {quickLinkItems.map(({ url, label }) => (
                <li key={url}>
                  <Link href={url} className={cn("hover:text-sky-600")}>
                    {label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div className={cn("flex-2 space-y-6 text-center md:text-left")}>
            <h2 className={cn("hidden md:block text-xl")}>Contact Us</h2>
            <ul
              className={cn(
                "flex flex-col items-center md:items-start space-y-3 text-gray-600",
              )}
            >
              {contactItems.map(({ url, icon, label }) => (
                <li key={url}>
                  <Link
                    href={url}
                    className={cn("flex items-center gap-3 hover:text-sky-600")}
                  >
                    <span className={cn("text-sky-600")}>{icon}</span>
                    <span>{label}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
      <div
        className={cn("bg-gradient-to-r from-sky-600 to-sky-500 text-white")}
      >
        <p className={cn("p-4 mx-auto max-w-7xl text-center")}>
          Copyright © 2025 Ecobuilt. All rights reserved. Privacy Policy | Terms
          of Service
        </p>
      </div>
    </footer>
  );
}
