"use client";

import type { VendorProfileType } from "~/lib/types";

import Image from "next/image";
import Link from "next/link";

import { MapPinIcon } from "lucide-react";

import { Card, CardContent, CardFooter } from "~/components/ui/card";
import { domine } from "~/lib/fonts";
import { routes } from "~/lib/routes";
import { cn } from "~/lib/utils";

interface VendorProps {
  vendor: VendorProfileType;
}

export function Vendor({ vendor }: Readonly<VendorProps>) {
  const truncatedDescription =
    vendor.description.length > 80
      ? `${vendor.description.substring(0, 80)}...`
      : vendor.description;

  return (
    <Link href={routes.app.public.vendors.url(vendor.id)}>
      <Card
        className={cn(
          "p-0 gap-0 hover:shadow-md transition-shadow duration-300 h-full",
        )}
      >
        <CardContent className={cn("p-0 relative")}>
          <div
            className={cn("bg-gray-50 p-6 flex justify-center items-center")}
          >
            <Image
              src={`${process.env.NEXT_PUBLIC_FILE_URL}/${vendor.pictureId}`}
              alt={vendor.name}
              width={120}
              height={120}
              className={cn("h-24 w-auto object-contain")}
            />
          </div>
        </CardContent>
        <CardFooter className={cn("p-5 flex-col items-stretch gap-3")}>
          <div>
            <h3 className={cn("text-xl font-bold mb-1", domine.className)}>
              {vendor.name}
            </h3>
            <p className={cn("text-sm text-muted-foreground")}>
              {truncatedDescription}
            </p>
          </div>
          <div className={cn("flex flex-col gap-2 mt-1")}>
            {vendor.city && (
              <div
                className={cn(
                  "flex items-center text-sm text-muted-foreground",
                )}
              >
                <MapPinIcon className="h-3.5 w-3.5 mr-1.5" />
                <span>
                  {vendor.city}
                  {vendor.postalCode ? `, ${vendor.postalCode}` : ""}
                </span>
              </div>
            )}
          </div>
        </CardFooter>
      </Card>
    </Link>
  );
}
