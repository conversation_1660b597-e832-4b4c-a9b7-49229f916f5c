"use client";

import type {
  PublicCategoryType,
  PublicProductType,
  VendorProfileType,
} from "~/lib/types";

import Image from "next/image";
import * as React from "react";

import { useStore } from "@nanostores/react";
import { Building2Icon, Loader2Icon, MinusIcon, PlusIcon } from "lucide-react";

import { VendorConfirmationDialog } from "~/app/(web)/_components/vendor-confirmation-dialog";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import {
  Carousel,
  type CarouselApi,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "~/components/ui/carousel";
import { Input } from "~/components/ui/input";
import { Separator } from "~/components/ui/separator";
import { useAuthContext } from "~/context/auth";
import { toast } from "sonner";
import { checkVendorCompatibility } from "~/lib/cart-utils";
import { routes } from "~/lib/routes";
import { formatPrice } from "~/lib/utils";
import { $cart, cartAPI } from "~/stores/cart";
import { useRouter } from "next/navigation";

interface ProductDetailsProps {
  product: PublicProductType & {
    category: PublicCategoryType;
    vendor: VendorProfileType;
  };
}

export function ProductDetails({ product }: Readonly<ProductDetailsProps>) {
  const cart = useStore($cart);
  const router = useRouter();
  const [api, setApi] = React.useState<CarouselApi>();
  const [currentSlide, setCurrentSlide] = React.useState(0);
  const [quantity, setQuantity] = React.useState(1);
  const [showVendorDialog, setShowVendorDialog] = React.useState(false);
  const [vendorDialogData, setVendorDialogData] = React.useState<{
    currentVendor: VendorProfileType | null;
    newVendor: VendorProfileType;
  } | null>(null);
  const { auth, token } = useAuthContext();

  React.useEffect(() => {
    if (!api) return;
    setCurrentSlide(api.selectedScrollSnap());
    api.on("select", () => setCurrentSlide(api.selectedScrollSnap()));
  }, [api]);

  const handleQuantityChange = (change: number) => {
    setQuantity((prev) => {
      const newValue = prev + change;
      if (newValue < 1) return 1;
      if (newValue > product.stock) return product.stock;
      return newValue;
    });
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number.parseInt(event.target.value, 10);
    if (Number.isNaN(value) || value < 1) setQuantity(1);
    else if (value > product.stock) setQuantity(product.stock);
    else setQuantity(value);
  };

  const addProductToCart = async (replaceExisting = false) => {
    if (quantity <= 0) return;

    if (!token) {
      toast.error("Please sign in to add items to cart");
      router.push(routes.app.auth.signIn.url());
      return;
    }

    try {
      await cartAPI.addToCart(token, product.id, quantity, replaceExisting);
      toast.success(`${product.name} added to cart`);
    } catch (error: any) {
      console.error("Failed to add product to cart:", error);
      toast.error(error.message || "Failed to add to cart");
    }
  };

  const handleAddToCart = () => {
    if (quantity <= 0) return;
    const { isSameVendor, currentVendor, newVendor } = checkVendorCompatibility(
      cart.items,
      product
    );

    if (isSameVendor) {
      addProductToCart();
      return;
    }

    if (currentVendor && newVendor) {
      setVendorDialogData({ currentVendor, newVendor });
      setShowVendorDialog(true);
    }
  };

  const handleVendorConfirm = () => {
    addProductToCart(true);
    setShowVendorDialog(false);
  };

  const handleVendorCancel = () => {
    setShowVendorDialog(false);
  };

  return (
    <div className="grid grid-cols-1 gap-12 md:grid-cols-2 lg:gap-16">
      <div className="space-y-6">
        <Carousel
          setApi={setApi}
          opts={{ loop: product.pictureIds.length > 1 }}
          className="overflow-hidden rounded-xl shadow-lg"
        >
          <CarouselContent>
            {product.pictureIds.map((id, index) => (
              <CarouselItem key={id}>
                <div className="aspect-square w-full overflow-hidden bg-gray-50">
                  <Image
                    src={`${process.env.NEXT_PUBLIC_FILE_URL}/${id}`}
                    alt={product.name}
                    width={800}
                    height={800}
                    className="h-full w-full object-contain"
                    priority={index === 0}
                  />
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>

          {product.pictureIds.length > 1 && (
            <>
              <CarouselPrevious className="left-4 size-10" />
              <CarouselNext className="right-4 size-10" />
            </>
          )}
        </Carousel>

        {product.pictureIds.length > 1 && (
          <div className="flex justify-center gap-3">
            {product.pictureIds.map((id, index) => (
              <button
                type="button"
                key={id}
                onClick={() => api?.scrollTo(index)}
                className={`size-20 overflow-hidden rounded-lg border-2 transition-all ${
                  index === currentSlide
                    ? "border-primary scale-105 shadow-md"
                    : "border-transparent opacity-70 hover:opacity-100"
                }`}
              >
                <Image
                  src={`${process.env.NEXT_PUBLIC_FILE_URL}/${id}`}
                  alt=""
                  width={80}
                  height={80}
                  className="h-full w-full object-cover"
                />
              </button>
            ))}
          </div>
        )}
      </div>

      <div className="space-y-8">
        <div className="space-y-4">
          <div className="space-y-2">
            <div className="flex flex-wrap gap-2">
              <Badge variant="secondary" className="text-sm font-medium">
                {product.category.name}
              </Badge>
              {product.isVerified && (
                <Badge className="text-sm font-medium bg-green-100 text-green-800 border-green-300">
                  EcoBuilt Verified
                </Badge>
              )}
            </div>
            <h1 className="text-3xl font-bold tracking-tight lg:text-4xl">
              {product.name}
            </h1>
          </div>

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Building2Icon className="size-4" />
            <span>{product.vendor.name}</span>
          </div>
        </div>

        <div className="space-y-1">
          <div className="flex items-baseline gap-2">
            <span className="text-3xl font-bold">
              {product.salePrice
                ? formatPrice(product.salePrice)
                : formatPrice(product.price)}
            </span>
            {product.salePrice && (
              <span className="text-lg text-muted-foreground line-through">
                {formatPrice(product.price)}
              </span>
            )}
          </div>
          <p className="text-sm text-muted-foreground">Excl. VAT</p>
        </div>

        <Separator className="bg-gray-200" />

        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Product Details</h2>
          <p className="text-muted-foreground">
            {product.description || "No description available."}
          </p>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="space-y-1">
              <p className="font-medium">Condition</p>
              <p className="text-muted-foreground">
                {product.condition === "EXCELLENT" && "Excellent"}
                {product.condition === "GOOD" && "Good"}
                {product.condition === "FAIR" && "Fair"}
                {!product.condition && "N/A"}
              </p>
            </div>
            <div className="space-y-1">
              <p className="font-medium">SKU</p>
              <p className="text-muted-foreground">
                {product.sku || "Not specified"}
              </p>
            </div>
            <div className="space-y-1">
              <p className="font-medium">Availability</p>
              <p
                className={
                  product.stock > 0 ? "text-green-600" : "text-red-600"
                }
              >
                {product.stock > 0
                  ? `${product.stock} in stock`
                  : "Out of stock"}
              </p>
            </div>
            {product.previousUsage && (
              <div className="space-y-1">
                <p className="font-medium">Previous Usage</p>
                <p className="text-muted-foreground">{product.previousUsage}</p>
              </div>
            )}
          </div>
        </div>

        <Separator className="bg-gray-200" />

        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="flex items-center rounded-lg border">
              <Button
                variant="ghost"
                size="icon"
                className="size-10 rounded-r-none"
                onClick={() => handleQuantityChange(-1)}
                disabled={quantity <= 1 || product.stock <= 0}
              >
                <MinusIcon className="size-4" />
              </Button>
              <Input
                type="number"
                min="1"
                max={product.stock}
                value={quantity}
                onChange={handleInputChange}
                className="h-10 w-16 rounded-none border-0 text-center"
                disabled={product.stock <= 0}
              />
              <Button
                variant="ghost"
                size="icon"
                className="size-10 rounded-l-none"
                onClick={() => handleQuantityChange(1)}
                disabled={quantity >= product.stock || product.stock <= 0}
              >
                <PlusIcon className="size-4" />
              </Button>
            </div>

            <Button
              variant="default-gradient"
              size="lg"
              className="flex-1"
              onClick={handleAddToCart}
              disabled={
                product.stock <= 0 ||
                (auth ? auth.role !== "USER" : false) ||
                cartAPI.isItemAdding(product.id)
              }
            >
              {cartAPI.isItemAdding(product.id) ? (
                <>
                  <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                  Adding...
                </>
              ) : product.stock <= 0 ? (
                "Out of Stock"
              ) : (
                "Add to Cart"
              )}
            </Button>
          </div>

          {product.stock > 0 && product.stock < 10 && (
            <p className="text-sm font-medium text-amber-600">
              Only {product.stock} left in stock - order soon!
            </p>
          )}
        </div>
      </div>

      {vendorDialogData?.currentVendor && (
        <VendorConfirmationDialog
          isOpen={showVendorDialog}
          onOpenChange={setShowVendorDialog}
          currentVendor={vendorDialogData.currentVendor}
          newVendor={vendorDialogData.newVendor}
          onConfirm={handleVendorConfirm}
          onCancel={handleVendorCancel}
        />
      )}
    </div>
  );
}
