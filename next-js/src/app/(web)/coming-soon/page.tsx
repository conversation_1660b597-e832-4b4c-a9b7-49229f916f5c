"use client";

import { useRouter, useSearchParams } from "next/navigation";

import { ArrowLeftIcon, ClockIcon } from "lucide-react";

import { Button } from "~/components/ui/button";
import { domine } from "~/lib/fonts";
import { routes } from "~/lib/routes";
import { cn } from "~/lib/utils";

export default function ComingSoonPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const featureName = searchParams.get("feature") || "This feature";
  const backRoute = searchParams.get("back");
  const backLabel = searchParams.get("backLabel") || "Go Back";

  const handleGoBack = () => {
    if (backRoute) {
      router.push(backRoute);
    } else {
      router.back();
    }
  };

  return (
    <section
      className={cn(
        "py-14 px-7 md:px-24 min-h-[calc(100svh_-_5rem)] md:min-h-[calc(100svh_-_12rem)] flex justify-center items-center",
      )}
    >
      <div className={cn("flex-1 flex flex-col items-center gap-12 max-w-3xl")}>
        <div className={cn("flex flex-col items-center gap-8 text-center")}>
          <div className={cn("relative")}>
            <div
              className={cn(
                "size-32 bg-gradient-to-r from-sky-600 to-sky-500 rounded-full flex items-center justify-center shadow-lg",
              )}
            >
              <ClockIcon
                className={cn("size-16 text-white")}
                strokeWidth={1.5}
              />
            </div>
            <div
              className={cn(
                "absolute -top-2 -right-2 size-12 bg-gradient-to-r from-sky-600 to-sky-500 rounded-full flex items-center justify-center shadow-lg",
              )}
            >
              <div
                className={cn("size-4 bg-white rounded-full animate-pulse")}
              />
            </div>
          </div>

          <div className={cn("space-y-4")}>
            <h2
              className={cn(
                "text-black/80 text-4xl lg:text-6xl font-medium",
                domine.className,
              )}
            >
              {featureName} is Coming Soon!
            </h2>
            <p
              className={cn(
                "text-muted-foreground text-xl md:text-lg lg:text-xl font-medium max-w-2xl",
              )}
            >
              We're working hard to bring you this amazing feature. Stay tuned
              for updates and exciting new functionality that will enhance your
              experience.
            </p>
          </div>
        </div>

        <div className={cn("flex flex-col sm:flex-row items-center gap-4")}>
          <Button
            onClick={handleGoBack}
            variant="secondary"
            size="xl"
            className={cn("flex items-center gap-2")}
          >
            <ArrowLeftIcon className="size-5" />
            {backLabel}
          </Button>
          <Button
            onClick={() => router.push(routes.app.public.home.url())}
            variant="default-gradient"
            size="xl"
            className={cn("min-w-32")}
          >
            Go to Home
          </Button>
        </div>
      </div>
    </section>
  );
}
