import type { PropsWithChildren } from "react";

import type { Metada<PERSON> } from "next";

import { SidebarProvider } from "~/components/ui/sidebar";

export const metadata: Metadata = {
  title: "EcoBuiltConnect - Dashboard",
  description: "EcoBuiltConnect - Dashboard",
};

export default function ProtectedLayout({
  children,
}: Readonly<PropsWithChildren>) {
  return (
    <>
      <SidebarProvider>{children}</SidebarProvider>
    </>
  );
}
