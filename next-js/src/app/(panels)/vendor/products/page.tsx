"use client";

import type { FormEvent } from "react";

import type {
  MultipleResponseType,
  ProductType,
  PublicCategoryType,
  VendorProfileType,
} from "~/lib/types";

import { useQuery } from "@tanstack/react-query";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import axios from "axios";
import { Package } from "lucide-react";

import { PageLayout } from "~/components/common/page-layout";
import { SearchFilterBar } from "~/components/layout/search-filter-bar";
import { DataTable } from "~/components/table/data-table";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Badge } from "~/components/ui/badge";
import { useAuthContext } from "~/context/auth";
import { routes } from "~/lib/routes";
import { cn, formatPrice } from "~/lib/utils";
import { DeleteProduct } from "./_components/delete-product";
import { EditProduct } from "./_components/edit-product";
import { FilterProducts } from "./_components/filter-products";
import { NewProduct } from "./_components/new-product";

async function getProducts({
  token,
  page = 1,
  limit = 10,
  sort = "",
  name = "",
  minStock,
  minPrice,
  maxPrice,
  isDeleted = false,
  categoryId = "",
  productRequestId,
}: {
  token: string | null;
  page?: number;
  limit?: number;
  sort?: string;
  name?: string;
  minStock?: number;
  minPrice?: number;
  maxPrice?: number;
  isDeleted?: boolean;
  categoryId?: string;
  productRequestId?: string;
}) {
  const params = new URLSearchParams({
    isDeleted: isDeleted.toString(),
    page: page.toString(),
    limit: limit.toString(),
  });

  if (sort) {
    params.append("sort", sort);
  }

  if (name) {
    params.append("name", name);
  }

  if (minStock !== undefined && minStock > 0) {
    params.append("minStock", minStock.toString());
  }

  if (minPrice !== undefined && minPrice > 0) {
    params.append("minPrice", minPrice.toString());
  }

  if (maxPrice !== undefined && maxPrice > 0) {
    params.append("maxPrice", maxPrice.toString());
  }

  if (categoryId) {
    params.append("categoryId", categoryId);
  }

  if (productRequestId) {
    params.append("productRequestId", productRequestId);
  }

  const url = `${routes.api.vendor.products.url()}?${params.toString()}`;

  const response = await axios.get(url, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return response.data;
}

export default function ProductsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const { token } = useAuthContext();

  const currentPage = Number(searchParams.get("page") || "1");
  const currentSort = searchParams.get("sort") || "";
  const currentName = searchParams.get("name") || "";
  const currentMinStock = searchParams.get("minStock")
    ? Number(searchParams.get("minStock"))
    : undefined;
  const currentMinPrice = searchParams.get("minPrice")
    ? Number(searchParams.get("minPrice"))
    : undefined;
  const currentMaxPrice = searchParams.get("maxPrice")
    ? Number(searchParams.get("maxPrice"))
    : undefined;
  const currentIsDeleted = searchParams.get("isDeleted") === "true";
  const currentCategoryId = searchParams.get("categoryId") || "";
  const currentProductRequestId = searchParams.get("productRequestId") || "";

  const [queryTerm, setQueryTerm] = useState(currentName);

  const {
    data: productsQuery,
    isLoading: productsQueryIsLoading,
    isError: productsQueryIsError,
  } = useQuery<
    MultipleResponseType<{
      products: (ProductType & {
        category: PublicCategoryType;
        vendor: VendorProfileType;
      })[];
    }>
  >({
    queryKey: [
      "products",
      currentPage,
      currentSort,
      currentName,
      currentMinStock,
      currentMinPrice,
      currentMaxPrice,
      currentIsDeleted,
      currentCategoryId,
    ],
    queryFn: () =>
      getProducts({
        token,
        page: currentPage,
        sort: currentSort,
        name: currentName,
        minStock: currentMinStock,
        minPrice: currentMinPrice,
        maxPrice: currentMaxPrice,
        isDeleted: currentIsDeleted,
        categoryId: currentCategoryId,
        productRequestId: currentProductRequestId,
      }),
  });

  const handleSearch = (event: FormEvent) => {
    event.preventDefault();

    const params = new URLSearchParams(searchParams.toString());

    if (queryTerm) {
      params.set("name", queryTerm);
    } else {
      params.delete("name");
    }

    params.delete("page");

    const newUrl = `${window.location.pathname}${
      params.toString() ? `?${params.toString()}` : ""
    }`;

    router.push(newUrl);
  };

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());

    if (page > 1) {
      params.set("page", page.toString());
    } else {
      params.delete("page");
    }

    const newUrl = `${window.location.pathname}${
      params.toString() ? `?${params.toString()}` : ""
    }`;
    router.push(newUrl);
  };

  useEffect(() => {
    setQueryTerm(currentName);
  }, [currentName]);

  return (
    <PageLayout
      title="Products"
      description="Manage your products inventory here. Add new products, update existing ones, and organize them."
      isLoading={productsQueryIsLoading}
      isError={productsQueryIsError || !productsQuery?.data?.products}
      errorTitle="Error Loading Products"
      errorDescription="We couldn't load your products information. Please try again later."
      actions={<NewProduct />}
    >
      <SearchFilterBar
        queryTerm={queryTerm}
        setQueryTerm={setQueryTerm}
        handleSearch={handleSearch}
        placeholder="Search Products..."
        filterComponent={<FilterProducts />}
      />
      <DataTable
        data={productsQuery?.data?.products}
        columns={[
          {
            header: "Product",
            cell: (product) => (
              <div className={cn("flex items-center gap-2")}>
                <Avatar className="size-10 rounded-md">
                  <AvatarImage
                    src={`${process.env.NEXT_PUBLIC_FILE_URL}/${product.pictureIds[0]}`}
                    alt={product.name}
                    className={cn("object-cover")}
                  />
                  <AvatarFallback className={cn("rounded-md")}>
                    {product.name
                      .split(" ")
                      .map((part) => part.charAt(0).toUpperCase())
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-col text-wrap">
                  <span className="text-sm font-medium">{product.name}</span>
                  <span className="text-xs text-muted-foreground">
                    {product.description}
                  </span>
                </div>
              </div>
            ),
          },
          {
            header: "Stock",
            cell: (product) => <span>{product.stock}</span>,
          },
          {
            header: "Condition",
            cell: (product) => (
              <Badge variant="outline">
                {product.condition === "EXCELLENT" && "Excellent"}
                {product.condition === "GOOD" && "Good"}
                {product.condition === "FAIR" && "Fair"}
                {!product.condition && "N/A"}
              </Badge>
            ),
          },
          {
            header: "Category",
            cell: (product) => (
              <Badge variant="outline">{product.category.name}</Badge>
            ),
          },
          {
            header: "Price",
            cell: (product) => <span>{formatPrice(product.price)}</span>,
          },
          {
            header: "",
            cell: (product) => (
              <div className={cn("space-x-2")}>
                <EditProduct product={product} />
                {!product.isDeleted && <DeleteProduct id={product.id} />}
              </div>
            ),
          },
        ]}
        currentPage={currentPage}
        meta={productsQuery?.meta}
        onPageChange={handlePageChange}
        itemName="products"
        emptyState={{
          icon: Package,
          title: "No products found",
          description:
            currentName ||
            currentCategoryId ||
            currentMinStock ||
            currentMinPrice ||
            currentMaxPrice ||
            currentIsDeleted
              ? "No products match your current filters. Try adjusting your search criteria."
              : "There are no products available at the moment.",
          action:
            currentName ||
            currentCategoryId ||
            currentMinStock ||
            currentMinPrice ||
            currentMaxPrice ||
            currentIsDeleted
              ? {
                  label: "Clear filters",
                  onClick: () => {
                    router.push(window.location.pathname);
                  },
                }
              : undefined,
        }}
      />
    </PageLayout>
  );
}
