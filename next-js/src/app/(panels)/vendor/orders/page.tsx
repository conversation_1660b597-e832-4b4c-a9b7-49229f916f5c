"use client";

import { useState } from "react";

import { useRouter, useSearchParams } from "next/navigation";

import { useQuery } from "@tanstack/react-query";

import axios from "axios";
import { ShoppingCartIcon } from "lucide-react";

import { PageLayout } from "~/components/common/page-layout";
import { SearchFilterBar } from "~/components/layout/search-filter-bar";
import { DataTable } from "~/components/table/data-table";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { useAuthContext } from "~/context/auth";
import { routes } from "~/lib/routes";
import type {
  CategoryType,
  MultipleResponseType,
  OrderStatus,
  ProductType,
  PublicDeliveryRequestType,
  PublicOrderToProductType,
  PublicOrderType,
  UserProfileType,
  VendorProfileType,
} from "~/lib/types";
import { cn, formatDate, formatPrice } from "~/lib/utils";
import { FilterOrders } from "./_components/filter-orders";
import { OrderDetail } from "./_components/order-detail";
import { OrderStatusSwitcher } from "./_components/order-status-switcher";

async function getOrders({
  token,
  page = 1,
  limit = 10,
  sort = "",
  status = "",
  productName = "",
  userName = "",
  minTotalPrice,
  maxTotalPrice,
  categoryId = "",
}: {
  token: string | null;
  page?: number;
  limit?: number;
  sort?: string;
  status?: string;
  productName?: string;
  userName?: string;
  minTotalPrice?: number;
  maxTotalPrice?: number;
  categoryId?: string;
}) {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });

  if (sort) {
    params.append("sort", sort);
  }

  if (status) {
    params.append("status", status);
  }

  if (productName) {
    params.append("productName", productName);
  }

  if (userName) {
    params.append("userName", userName);
  }

  if (minTotalPrice !== undefined && minTotalPrice > 0) {
    params.append("minTotalPrice", minTotalPrice.toString());
  }

  if (maxTotalPrice !== undefined && maxTotalPrice > 0) {
    params.append("maxTotalPrice", maxTotalPrice.toString());
  }

  if (categoryId) {
    params.append("categoryId", categoryId);
  }

  const response = await axios.get(
    `${routes.api.vendor.orders.url()}?${params.toString()}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );

  return response.data;
}

export default function OrdersPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const { token } = useAuthContext();

  const currentPage = Number(searchParams.get("page") || "1");
  const currentSort = searchParams.get("sort") || "";
  const currentStatus = searchParams.get("status") || "";
  const currentProductName = searchParams.get("productName") || "";
  const currentUserName = searchParams.get("userName") || "";
  const currentMinTotalPrice = searchParams.get("minTotalPrice")
    ? Number(searchParams.get("minTotalPrice"))
    : undefined;
  const currentMaxTotalPrice = searchParams.get("maxTotalPrice")
    ? Number(searchParams.get("maxTotalPrice"))
    : undefined;
  const currentCategoryId = searchParams.get("categoryId") || "";

  const [queryTerm, setQueryTerm] = useState(currentUserName);

  const {
    data: ordersQuery,
    isLoading: ordersQueryIsLoading,
    isError: ordersQueryIsError,
  } = useQuery<
    MultipleResponseType<{
      orders: (PublicOrderType & {
        orderToProduct: (PublicOrderToProductType & {
          product: ProductType & {
            category: CategoryType;
            vendor: VendorProfileType;
          };
        })[];
        user: UserProfileType;
        deliveryRequest: PublicDeliveryRequestType | null;
      })[];
    }>
  >({
    queryKey: [
      "orders",
      currentPage,
      currentSort,
      currentStatus,
      currentProductName,
      currentUserName,
      currentMinTotalPrice,
      currentMaxTotalPrice,
      currentCategoryId,
    ],
    queryFn: () =>
      getOrders({
        token,
        page: currentPage,
        sort: currentSort,
        status: currentStatus,
        productName: currentProductName,
        userName: currentUserName,
        minTotalPrice: currentMinTotalPrice,
        maxTotalPrice: currentMaxTotalPrice,
        categoryId: currentCategoryId,
      }),
  });

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const params = new URLSearchParams(window.location.search);

    if (queryTerm) {
      params.set("userName", queryTerm);
    } else {
      params.delete("userName");
    }

    params.delete("page");

    const newUrl = `${window.location.pathname}${
      params.toString() ? `?${params.toString()}` : ""
    }`;
    router.push(newUrl);
  };

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(window.location.search);
    params.set("page", page.toString());
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    router.push(newUrl);
  };

  const orders = ordersQuery?.data?.orders || [];

  return (
    <PageLayout
      title="Orders"
      description="View and manage customer orders. You can search, filter and update order status from here."
      isLoading={ordersQueryIsLoading}
      isError={ordersQueryIsError}
      errorTitle="Failed to load orders"
      errorDescription="We couldn't load your orders information. Please try again."
    >
      <SearchFilterBar
        queryTerm={queryTerm}
        setQueryTerm={setQueryTerm}
        handleSearch={handleSearch}
        placeholder="Search by Customer..."
        filterComponent={<FilterOrders />}
      />
      <DataTable
        data={orders}
        columns={[
          {
            header: "Customer",
            cell: (order) => (
              <div className={cn("flex items-center gap-2")}>
                <Avatar className="size-10">
                  <AvatarImage
                    src={`${process.env.NEXT_PUBLIC_FILE_URL}/${order.user.pictureId}`}
                    alt={order.user.name}
                    className={cn("object-cover")}
                  />
                  <AvatarFallback>
                    {order.user.name
                      .split(" ")
                      .map((part) => part.charAt(0).toUpperCase())
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-col text-wrap">
                  <span className="text-sm font-medium">{order.user.name}</span>
                  <span className="text-xs text-muted-foreground">
                    {order.user.phone}
                  </span>
                </div>
              </div>
            ),
          },
          {
            header: "Order ID",
            cell: (order) => <span>#{order.id.slice(0, 8)}</span>,
          },
          {
            header: "Items",
            cell: (order) => <span>{order.orderToProduct.length}</span>,
          },
          {
            header: "Total Price",
            cell: (order) => <span>{formatPrice(order.totalPrice)}</span>,
          },
          {
            header: "Status",
            cell: (order) => (
              <OrderStatusSwitcher
                id={order.id}
                status={order.status as keyof typeof OrderStatus}
              />
            ),
          },
          {
            header: "Delivery Option",
            cell: (order) => (
              <span className="capitalize">
                {order.deliveryOption.toLowerCase().replace("_", " ")}
              </span>
            ),
          },
          {
            header: "Date Ordered",
            cell: (order) => <span>{formatDate(order.createdAt)}</span>,
          },
          {
            header: "",
            cell: (order) => <OrderDetail order={order} />,
          },
        ]}
        currentPage={currentPage}
        meta={ordersQuery?.meta}
        onPageChange={handlePageChange}
        itemName="orders"
        emptyState={{
          icon: ShoppingCartIcon,
          title: "No orders found",
          description: "No orders match your current filters.",
        }}
      />
    </PageLayout>
  );
}
