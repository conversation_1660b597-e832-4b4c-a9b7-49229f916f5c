"use client";

import type { FormEvent } from "react";

import type {
  MultipleResponseType,
  PublicCategoryType,
  PublicProductRequestType,
  PublicProductType,
  UserProfileType,
} from "~/lib/types";

import { useQuery } from "@tanstack/react-query";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import axios from "axios";
import { ExternalLinkIcon, Package } from "lucide-react";

import Link from "next/link";
import { PageLayout } from "~/components/common/page-layout";
import { SearchFilterBar } from "~/components/layout/search-filter-bar";
import { DataTable } from "~/components/table/data-table";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Badge } from "~/components/ui/badge";
import { buttonVariants } from "~/components/ui/button";
import { useAuthContext } from "~/context/auth";
import { routes } from "~/lib/routes";
import { cn, formatPrice } from "~/lib/utils";
import { FilterProductRequests } from "./_components/filter-product-requests";
import { NewProduct } from "./_components/new-product";

async function getProductRequests({
  token,
  name = "",
  page = 1,
  limit = 10,
  sort = "",
  minQuantity,
  minPrice,
  maxPrice,
  categoryId = "",
}: {
  token: string | null;
  name?: string;
  page?: number;
  limit?: number;
  sort?: string;
  minQuantity?: number;
  minPrice?: number;
  maxPrice?: number;
  categoryId?: string;
}) {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });

  if (sort) {
    params.append("sort", sort);
  }

  if (name) {
    params.append("name", name);
  }

  if (minQuantity !== undefined && minQuantity > 0) {
    params.append("minQuantity", minQuantity.toString());
  }

  if (minPrice !== undefined && minPrice > 0) {
    params.append("minPrice", minPrice.toString());
  }

  if (maxPrice !== undefined && maxPrice > 0) {
    params.append("maxPrice", maxPrice.toString());
  }

  if (categoryId) {
    params.append("categoryId", categoryId);
  }

  const url = `${routes.api.vendor.productRequests.url()}?${params.toString()}`;

  const response = await axios.get(url, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return response.data;
}

export default function ProductRequestsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const { token } = useAuthContext();

  const currentPage = Number(searchParams.get("page") || "1");
  const currentSort = searchParams.get("sort") || "";
  const currentName = searchParams.get("name") || "";
  const currentMinQuantity = searchParams.get("minQuantity")
    ? Number(searchParams.get("minQuantity"))
    : undefined;
  const currentMinPrice = searchParams.get("minPrice")
    ? Number(searchParams.get("minPrice"))
    : undefined;
  const currentMaxPrice = searchParams.get("maxPrice")
    ? Number(searchParams.get("maxPrice"))
    : undefined;
  const currentCategoryId = searchParams.get("categoryId") || "";

  const [queryTerm, setQueryTerm] = useState(currentName);

  const {
    data: productRequestQuery,
    isLoading: productRequestQueryIsLoading,
    isError: productRequestQueryIsError,
  } = useQuery<
    MultipleResponseType<{
      productRequests: (PublicProductRequestType & {
        category: PublicCategoryType;
        products: PublicProductType[];
        user: UserProfileType;
      })[];
    }>
  >({
    queryKey: [
      "product-requests",
      currentPage,
      currentSort,
      currentName,
      currentMinQuantity,
      currentMinPrice,
      currentMaxPrice,
      currentCategoryId,
    ],
    queryFn: () =>
      getProductRequests({
        token,
        page: currentPage,
        sort: currentSort,
        name: currentName,
        minQuantity: currentMinQuantity,
        minPrice: currentMinPrice,
        maxPrice: currentMaxPrice,
        categoryId: currentCategoryId,
      }),
  });

  const handleSearch = (event: FormEvent) => {
    event.preventDefault();

    const params = new URLSearchParams(searchParams.toString());

    if (queryTerm) {
      params.set("name", queryTerm);
    } else {
      params.delete("name");
    }

    params.delete("page");

    const newUrl = `${window.location.pathname}${
      params.toString() ? `?${params.toString()}` : ""
    }`;

    router.push(newUrl);
  };

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());

    if (page > 1) {
      params.set("page", page.toString());
    } else {
      params.delete("page");
    }

    const newUrl = `${window.location.pathname}${
      params.toString() ? `?${params.toString()}` : ""
    }`;
    router.push(newUrl);
  };

  useEffect(() => {
    setQueryTerm(currentName);
  }, [currentName]);

  return (
    <PageLayout
      title="Product Requests"
      description="Manage your product requests here. Add new productRequest, update existing ones, and organize them."
      isLoading={productRequestQueryIsLoading}
      isError={
        productRequestQueryIsError ||
        !productRequestQuery?.data?.productRequests
      }
      errorTitle="Error Loading Products"
      errorDescription="We couldn't load your productRequest information. Please try again later."
    >
      <SearchFilterBar
        queryTerm={queryTerm}
        setQueryTerm={setQueryTerm}
        handleSearch={handleSearch}
        placeholder="Search Products..."
        filterComponent={<FilterProductRequests />}
      />
      <DataTable
        data={productRequestQuery?.data?.productRequests}
        columns={[
          {
            header: "Product",
            cell: (productRequest) => (
              <div className={cn("flex items-center gap-2")}>
                <Avatar className="size-10 rounded-md">
                  <AvatarImage
                    src={`${process.env.NEXT_PUBLIC_FILE_URL}/${productRequest.pictureIds[0]}`}
                    alt={productRequest.name}
                    className={cn("object-cover")}
                  />
                  <AvatarFallback className={cn("rounded-md")}>
                    {productRequest.name
                      .split(" ")
                      .map((part) => part.charAt(0).toUpperCase())
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-col text-wrap">
                  <span className="text-sm font-medium">
                    {productRequest.name}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {productRequest.description}
                  </span>
                </div>
              </div>
            ),
          },
          {
            header: "Category",
            cell: (productRequest) => (
              <Badge variant="outline">{productRequest.category.name}</Badge>
            ),
          },
          {
            header: "Quantity",
            cell: (productRequest) => <span>{productRequest.quantity}</span>,
          },
          {
            header: "Price",
            cell: (productRequest) => (
              <span>{formatPrice(productRequest.price)}</span>
            ),
          },
          {
            header: "",
            cell: (productRequest) => (
              <div className={cn("space-x-2")}>
                <NewProduct productRequest={productRequest} />
                <Link
                  href={`${routes.app.vendor.products.url()}?productRequestId=${
                    productRequest.id
                  }`}
                  className={cn(
                    buttonVariants({
                      variant: "outline",
                      size: "icon",
                      className: "size-8",
                    }),
                  )}
                >
                  <ExternalLinkIcon className="size-4" />
                </Link>
              </div>
            ),
          },
        ]}
        currentPage={currentPage}
        meta={productRequestQuery?.meta}
        onPageChange={handlePageChange}
        itemName="Product Requests"
        emptyState={{
          icon: Package,
          title: "No product requests found",
          description:
            currentName ||
            currentMinQuantity ||
            currentMinPrice ||
            currentMaxPrice ||
            currentCategoryId
              ? "No product requests match your current filters. Try adjusting your search criteria."
              : "There are no product requests available at the moment.",
          action:
            currentName ||
            currentMinQuantity ||
            currentMinPrice ||
            currentMaxPrice ||
            currentCategoryId
              ? {
                  label: "Clear filters",
                  onClick: () => {
                    router.push(window.location.pathname);
                  },
                }
              : undefined,
        }}
      />
    </PageLayout>
  );
}
