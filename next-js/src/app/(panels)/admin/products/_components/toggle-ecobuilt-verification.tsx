"use client";

import { useState } from "react";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import axios, { AxiosError } from "axios";
import { CheckCircle2Icon, Loader2Icon, ShieldIcon } from "lucide-react";
import { toast } from "sonner";

import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { useAuthContext } from "~/context/auth";
import { routes } from "~/lib/routes";
import { cn } from "~/lib/utils";

async function toggleEcoBuiltVerification({
  token,
  id,
  isVerified,
}: {
  token: string | null;
  id: string;
  isVerified: boolean;
}) {
  const response = await axios.put(
    routes.api.admin.products.ecoBuiltVerification(id),
    {
      isVerified,
    },
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );

  return response.data;
}

export function ToggleEcoBuiltVerification({
  id,
  isVerified,
}: {
  id: string;
  isVerified: boolean;
}) {
  const queryClient = useQueryClient();
  const { token } = useAuthContext();

  const [isVerificationDialogOpen, setIsVerificationDialogOpen] =
    useState(false);

  const verificationMutation = useMutation({
    mutationFn: toggleEcoBuiltVerification,
    onSuccess: ({ info }) => {
      toast.success(
        info.message || "Product verification status updated successfully",
      );
      setIsVerificationDialogOpen(false);
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
    onError: (error) => {
      if (error instanceof AxiosError) {
        toast.error(
          error.response?.data.info?.message ||
            "Failed to update verification status",
        );
      } else {
        toast.error("An unexpected error occurred");
      }
    },
  });

  return (
    <Dialog
      open={isVerificationDialogOpen}
      onOpenChange={setIsVerificationDialogOpen}
    >
      <DialogTrigger asChild>
        <Badge
          variant={isVerified ? "secondary" : "outline"}
          className="cursor-pointer flex items-center hover:opacity-80 transition-opacity"
        >
          {isVerified ? (
            <CheckCircle2Icon className="size-4 mr-1 text-green-500" />
          ) : (
            <ShieldIcon className="size-4 mr-1" />
          )}
          {isVerified ? "Verified" : "Not Verified"}
        </Badge>
      </DialogTrigger>
      <DialogContent className="sm:max-w-sm">
        <DialogHeader>
          <DialogTitle>Update Verification Status</DialogTitle>
          <DialogDescription>
            {isVerified
              ? "Remove the verification status from this product?"
              : "Mark this product as verified?"}
          </DialogDescription>
        </DialogHeader>

        <DialogFooter>
          <Button
            variant="outline"
            size="lg"
            className={cn("flex-1")}
            onClick={() => setIsVerificationDialogOpen(false)}
          >
            Cancel
          </Button>
          <Button
            variant={isVerified ? "destructive" : "default-gradient"}
            size="lg"
            className={cn("flex-1")}
            disabled={verificationMutation.isPending}
            onClick={() =>
              verificationMutation.mutate({
                token,
                id,
                isVerified: !isVerified,
              })
            }
          >
            {verificationMutation.isPending && (
              <Loader2Icon className={cn("mr-2 size-4 animate-spin")} />
            )}
            {isVerified ? "Remove Verification" : "Verify Product"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
