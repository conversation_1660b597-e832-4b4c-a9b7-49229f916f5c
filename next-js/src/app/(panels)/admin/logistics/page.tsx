"use client";

import type { FormEvent } from "react";

import type {
  AuthType,
  LogisticProviderProfileType,
  MultipleResponseType,
} from "~/lib/types";

import { useQuery } from "@tanstack/react-query";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import axios from "axios";

import { PageLayout } from "~/components/common/page-layout";
import { SearchFilterBar } from "~/components/layout/search-filter-bar";
import { DataTable } from "~/components/table/data-table";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Badge } from "~/components/ui/badge";
import { useAuthContext } from "~/context/auth";
import { routes } from "~/lib/routes";
import { cn } from "~/lib/utils";
import { ToggleDeleteLogisticProvider } from "./_components/delete-logistic-provider";
import { FilterLogisticProviders } from "./_components/filter-logistic-providers";
import { LogisticProviderStatusSwitcher } from "./_components/logistic-provider-status-switcher";

async function getLogisticProviders({
  token,
  page = 1,
  limit = 10,
  sort = "",
  name = "",
  phone = "",
  postalCode = "",
  city = "",
  status,
  isVerified = true,
  isDeleted = false,
}: {
  token: string | null;
  page?: number;
  limit?: number;
  sort?: string;
  name?: string;
  phone?: string;
  postalCode?: string;
  city?: string;
  status?: string;
  isVerified?: boolean;
  isDeleted?: boolean;
}) {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    isVerified: isVerified.toString(),
    isDeleted: isDeleted.toString(),
  });

  if (sort) {
    params.append("sort", sort);
  }

  if (name) {
    params.append("name", name);
  }

  if (phone) {
    params.append("phone", phone);
  }

  if (postalCode) {
    params.append("postalCode", postalCode);
  }

  if (city) {
    params.append("city", city);
  }

  if (status) {
    params.append("status", status);
  }

  const url = `${routes.api.admin.logisticProviders.url()}?${params.toString()}`;

  const response = await axios.get(url, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return response.data;
}

export default function LogisticProvidersPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const { token } = useAuthContext();

  const currentPage = Number(searchParams.get("page") || "1");
  const currentSort = searchParams.get("sort") || "";
  const currentName = searchParams.get("name") || "";
  const currentPhone = searchParams.get("phone") || "";
  const currentPostalCode = searchParams.get("postalCode") || "";
  const currentCity = searchParams.get("city") || "";
  const currentStatus = searchParams.get("status") || undefined;
  const currentIsVerified = searchParams.get("isVerified") !== "false";
  const currentIsDeleted = searchParams.get("isDeleted") === "true";

  const [queryTerm, setQueryTerm] = useState(currentName);

  const {
    data: logisticProvidersQuery,
    isLoading: logisticProvidersQueryIsLoading,
    isError: logisticProvidersQueryIsError,
  } = useQuery<
    MultipleResponseType<{
      logisticProviders: (LogisticProviderProfileType & {
        auth: AuthType;
      })[];
    }>
  >({
    queryKey: [
      "logisticProviders",
      currentPage,
      currentSort,
      currentName,
      currentPhone,
      currentPostalCode,
      currentCity,
      currentStatus,
      currentIsVerified,
      currentIsDeleted,
    ],
    queryFn: () =>
      getLogisticProviders({
        token,
        page: currentPage,
        name: currentName,
        phone: currentPhone,
        postalCode: currentPostalCode,
        city: currentCity,
        status: currentStatus,
        sort: currentSort,
        isVerified: currentIsVerified,
        isDeleted: currentIsDeleted,
      }),
  });

  const handleSearch = (event: FormEvent) => {
    event.preventDefault();

    const params = new URLSearchParams(searchParams.toString());

    if (queryTerm) {
      params.set("name", queryTerm);
    } else {
      params.delete("name");
    }

    params.delete("page");

    const newUrl = `${window.location.pathname}${
      params.toString() ? `?${params.toString()}` : ""
    }`;

    router.push(newUrl);
  };

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());

    if (page > 1) {
      params.set("page", page.toString());
    } else {
      params.delete("page");
    }

    const newUrl = `${window.location.pathname}${
      params.toString() ? `?${params.toString()}` : ""
    }`;
    router.push(newUrl);
  };

  useEffect(() => {
    setQueryTerm(currentName);
  }, [currentName]);

  return (
    <PageLayout
      title="Logistic Providers"
      description="Manage your logistic providers here. Add new logistic providers, update existing ones, and organize them."
      isLoading={logisticProvidersQueryIsLoading}
      isError={
        logisticProvidersQueryIsError ||
        !logisticProvidersQuery?.data?.logisticProviders
      }
      errorTitle="Error Loading Logistic Providers"
      errorDescription="We couldn't load your logistic providers information. Please try again later."
    >
      <SearchFilterBar
        queryTerm={queryTerm}
        setQueryTerm={setQueryTerm}
        handleSearch={handleSearch}
        placeholder="Search Logistic Providers..."
        filterComponent={<FilterLogisticProviders />}
      />
      <DataTable
        data={logisticProvidersQuery?.data?.logisticProviders}
        columns={[
          {
            header: "Logistic Provider",
            cell: (logisticProvider) => (
              <div className={cn("flex items-center gap-2")}>
                <Avatar className="size-10">
                  <AvatarImage
                    src={`${process.env.NEXT_PUBLIC_FILE_URL}/${logisticProvider.pictureId}`}
                    alt={logisticProvider.name}
                    className={cn("object-cover")}
                  />
                  <AvatarFallback>
                    {logisticProvider.name
                      .split(" ")
                      .map((part) => part.charAt(0).toUpperCase())
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-col text-wrap">
                  <span className="text-sm font-medium">
                    {logisticProvider.name}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {logisticProvider.auth.email}
                  </span>
                </div>
              </div>
            ),
          },
          {
            header: "Phone",
            cell: (logisticProvider) => <span>{logisticProvider.phone}</span>,
          },
          {
            header: "Pickup Address",
            cell: (logisticProvider) => (
              <div className="flex flex-col text-wrap">
                <span className="text-sm font-medium">
                  {logisticProvider.address}
                </span>
                <span className="text-xs text-muted-foreground">
                  {[logisticProvider.city, logisticProvider.postalCode]
                    .filter(Boolean)
                    .join(", ") || "N/A"}
                </span>
              </div>
            ),
          },
          {
            header: "Verification",
            cell: (logisticProvider) => (
              <Badge variant="outline">
                {logisticProvider.auth.isVerified ? "Verified" : "Not Verified"}
              </Badge>
            ),
          },
          {
            header: "Status",
            cell: (logisticProvider) => (
              <LogisticProviderStatusSwitcher
                id={logisticProvider.id}
                currentStatus={
                  logisticProvider.auth.status as
                    | "PENDING"
                    | "APPROVED"
                    | "REJECTED"
                }
              />
            ),
          },
          {
            header: "",
            cell: (logisticProvider) => (
              <div className={cn("space-x-2")}>
                <ToggleDeleteLogisticProvider
                  id={logisticProvider.id}
                  isDeleted={logisticProvider.auth.isDeleted}
                />
              </div>
            ),
          },
        ]}
        currentPage={currentPage}
        meta={logisticProvidersQuery?.meta}
        onPageChange={handlePageChange}
        itemName="logistic providers"
        emptyState={{
          title: "No logistic providers found",
          description:
            "There are no logistic providers available at the moment.",
        }}
      />
    </PageLayout>
  );
}
