"use client";

import { useState } from "react";

import Image from "next/image";

import {
  CheckCircle2Icon,
  ClockIcon,
  ExternalLinkIcon,
  PackageIcon,
  PhoneIcon,
  ShieldAlertIcon,
  TagIcon,
  TruckIcon,
} from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { Separator } from "~/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { domine } from "~/lib/fonts";
import type {
  PublicCategoryType,
  PublicOrderToProductType,
  PublicOrderType,
  PublicProductType,
  UserProfileType,
  VendorProfileType,
} from "~/lib/types";
import { cn, formatDate, formatPrice } from "~/lib/utils";

type OrderDetailProps = {
  order: PublicOrderType & {
    orderToProduct: (PublicOrderToProductType & {
      product: PublicProductType & {
        category: PublicCategoryType;
        vendor: VendorProfileType;
      };
    })[];
    user: UserProfileType;
  };
};

export function OrderDetail({ order }: OrderDetailProps) {
  const [isOpen, setIsOpen] = useState(false);

  const getOrderStatusVariant = () => {
    switch (order.status) {
      case "PENDING":
        return "default-gradient";
      case "PROCESSING":
      case "READY":
        return "secondary";
      case "COMPLETED":
        return "outline";
      default:
        return "outline";
    }
  };

  const OrderStatusIcon = () => {
    switch (order.status) {
      case "PENDING":
        return <ClockIcon className="size-4 mr-1" />;
      case "PROCESSING":
        return <PackageIcon className="size-4 mr-1" />;
      case "READY":
        return <TruckIcon className="size-4 mr-1" />;
      case "COMPLETED":
        return <CheckCircle2Icon className="size-4 mr-1" />;
      default:
        return <ShieldAlertIcon className="size-4 mr-1" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm">
          <ExternalLinkIcon className="size-4" />
          <span className="sr-only">View Order Details</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className={cn("text-xl", domine.className)}>
            Order Details
          </DialogTitle>
          <DialogDescription
            className={cn("flex justify-between items-center gap-2")}
          >
            <span>Order #{order.id.slice(0, 8)}</span>
            <Badge
              variant={getOrderStatusVariant()}
              className="cursor-pointer flex items-center hover:opacity-80 transition-opacity capitalize"
            >
              <OrderStatusIcon />
              {order.status.toLowerCase().replace("_", " ")}
            </Badge>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-3">
            <h3 className={cn("text-sm font-semibold", domine.className)}>
              Pickup Details
            </h3>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Avatar className="size-10">
                <AvatarImage
                  src={`${process.env.NEXT_PUBLIC_FILE_URL}/${order.orderToProduct[0].product.vendor.pictureId}`}
                  alt={order.orderToProduct[0].product.vendor.name}
                  className={cn("object-cover")}
                />
                <AvatarFallback>
                  {order.orderToProduct[0].product.vendor.name
                    .split(" ")
                    .map((part) => part.charAt(0).toUpperCase())
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">
                  {order.orderToProduct[0].product.vendor.name}
                </p>
                <div className="flex items-center text-xs text-muted-foreground gap-1">
                  <PhoneIcon className="size-3" />
                  {order.orderToProduct[0].product.vendor.phone}
                </div>
              </div>
            </div>
            <div className="text-right text-sm">
              <div className="flex items-center justify-end gap-1 text-muted-foreground">
                <span>{order.orderToProduct[0].product.vendor.city}</span>
              </div>
              <div className="flex items-center justify-end gap-1 text-muted-foreground mt-1">
                <span>{order.orderToProduct[0].product.vendor.postalCode}</span>
              </div>
            </div>
          </div>
          <div className="text-right text-sm text-muted-foreground">
            <span>{order.orderToProduct[0].product.vendor.pickupAddress}</span>
          </div>

          <Separator />

          <div className="flex items-center gap-2 mb-3">
            <h3 className={cn("text-sm font-semibold", domine.className)}>
              Delivery Details
            </h3>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Avatar className="size-10">
                <AvatarImage
                  src={`${process.env.NEXT_PUBLIC_FILE_URL}/${order.user.pictureId}`}
                  alt={order.user.name}
                  className={cn("object-cover")}
                />
                <AvatarFallback>
                  {order.user.name
                    .split(" ")
                    .map((part) => part.charAt(0).toUpperCase())
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">{order.user.name}</p>
                <div className="flex items-center text-xs text-muted-foreground gap-1">
                  <PhoneIcon className="size-3" />
                  {order.user.phone}
                </div>
              </div>
            </div>
            <div className="text-right text-sm">
              <div className="flex items-center justify-end gap-1 text-muted-foreground">
                <span>{order.user.city}</span>
              </div>
              <div className="flex items-center justify-end gap-1 text-muted-foreground mt-1">
                <span>{order.user.postalCode}</span>
              </div>
            </div>
          </div>
          <div className="text-right text-sm text-muted-foreground">
            <span>{order.user.deliveryAddress}</span>
          </div>

          <Separator />

          <div>
            <div className="flex items-center gap-2 mb-3">
              <TagIcon className="size-4 text-primary" />
              <h3 className={cn("text-sm font-semibold", domine.className)}>
                Order Items ({order.orderToProduct.length})
              </h3>
            </div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead className="text-center">Qty</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {order.orderToProduct.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="relative size-8 overflow-hidden rounded-md border">
                          <Image
                            src={`${process.env.NEXT_PUBLIC_FILE_URL}/${item.product.pictureIds[0]}`}
                            alt={item.product.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div>
                          <p className="text-sm font-medium">
                            {item.product.name}
                          </p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      {item.quantity}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
