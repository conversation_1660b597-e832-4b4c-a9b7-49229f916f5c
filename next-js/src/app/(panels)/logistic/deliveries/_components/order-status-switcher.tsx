"use client";

import { useState } from "react";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import axios, { AxiosError } from "axios";
import {
  CheckCircle2Icon,
  ClockIcon,
  Loader2Icon,
  PackageIcon,
  ShieldAlertIcon,
  TruckIcon,
} from "lucide-react";
import { toast } from "sonner";

import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { useAuthContext } from "~/context/auth";
import { routes } from "~/lib/routes";
import type { DeliveryRequestStatus } from "~/lib/types";
import { cn } from "~/lib/utils";

type DeliveryRequestStatusType = keyof typeof DeliveryRequestStatus;

async function updateDeliveryRequestStatus({
  token,
  id,
  status,
}: {
  token: string | null;
  id: string;
  status: DeliveryRequestStatusType;
}) {
  console.log("Updating delivery request status", { id, status });

  const response = await axios.put(
    routes.api.logisticProvider.deliveryRequests.url(id),
    {
      status,
    },
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );

  return response.data;
}

export function DeliveryStatusSwitcher({
  id,
  status,
}: {
  id: string;
  status: DeliveryRequestStatusType;
}) {
  const queryClient = useQueryClient();
  const { token } = useAuthContext();

  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] =
    useState<DeliveryRequestStatusType>(status);

  const handleDialogChange = (open: boolean) => {
    if (open) {
      setSelectedStatus(status);
    }
    setIsStatusDialogOpen(open);
  };

  const statusUpdateMutation = useMutation({
    mutationFn: updateDeliveryRequestStatus,
    onSuccess: ({ info }) => {
      toast.success(info.message || "Delivery status updated successfully");
      setIsStatusDialogOpen(false);
      queryClient.invalidateQueries({ queryKey: ["deliveries"] });
    },
    onError: (error) => {
      if (error instanceof AxiosError) {
        toast.error(
          error.response?.data.info?.message ||
            "Failed to update delivery status",
        );
      } else {
        toast.error("An unexpected error occurred");
      }
    },
  });

  const getStatusVariant = () => {
    switch (status) {
      case "PENDING":
        return "default-gradient";
      case "PROPOSED":
      case "IN_TRANSIT":
        return "secondary";
      case "PROCESSING":
      case "DELIVERED":
        return "outline";
      default:
        return "outline";
    }
  };

  const StatusIcon = () => {
    switch (status) {
      case "PROPOSED":
      case "PENDING":
        return <ClockIcon className="size-4 mr-1" />;
      case "PROCESSING":
        return <PackageIcon className="size-4 mr-1" />;
      case "IN_TRANSIT":
        return <TruckIcon className="size-4 mr-1" />;
      case "DELIVERED":
        return <CheckCircle2Icon className="size-4 mr-1" />;
      default:
        return <ShieldAlertIcon className="size-4 mr-1" />;
    }
  };

  return (
    <Dialog open={isStatusDialogOpen} onOpenChange={handleDialogChange}>
      <DialogTrigger asChild>
        <Badge
          variant={getStatusVariant()}
          className="cursor-pointer flex items-center hover:opacity-80 transition-opacity capitalize"
        >
          <StatusIcon />
          {status.toLowerCase().replace("_", " ")}
        </Badge>
      </DialogTrigger>
      <DialogContent className="sm:max-w-sm">
        <DialogHeader>
          <DialogTitle>Update Delivery Status</DialogTitle>
          <DialogDescription>
            Change the delivery's status. This will affect the delivery
            processing workflow.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <Select
            value={selectedStatus}
            onValueChange={(value) =>
              setSelectedStatus(value as DeliveryRequestStatusType)
            }
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select new status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="IN_TRANSIT">In Transit</SelectItem>
              <SelectItem value="DELIVERED">Delivered</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            size="lg"
            className={cn("flex-1")}
            onClick={() => setIsStatusDialogOpen(false)}
          >
            Cancel
          </Button>
          <Button
            variant="default-gradient"
            size="lg"
            className={cn("flex-1")}
            disabled={
              statusUpdateMutation.isPending || selectedStatus === status
            }
            onClick={() =>
              statusUpdateMutation.mutate({
                token,
                id,
                status: selectedStatus,
              })
            }
          >
            {statusUpdateMutation.isPending && (
              <Loader2Icon className={cn("mr-2 size-4 animate-spin")} />
            )}
            Update
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
