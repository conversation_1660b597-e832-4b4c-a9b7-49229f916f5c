"use client";

import { useRouter, useSearch<PERSON>ara<PERSON> } from "next/navigation";

import { useQuery } from "@tanstack/react-query";

import axios from "axios";
import { ShoppingCartIcon } from "lucide-react";

import { PageLayout } from "~/components/common/page-layout";
import { DataTable } from "~/components/table/data-table";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { useAuthContext } from "~/context/auth";
import { routes } from "~/lib/routes";
import type {
  MultipleResponseType,
  PublicCategoryType,
  PublicDeliveryRequestType,
  PublicOrderToProductType,
  PublicOrderType,
  PublicProductType,
  UserProfileType,
  VendorProfileType,
} from "~/lib/types";
import { cn, formatDate, formatPrice } from "~/lib/utils";
import { OfferDeliveryButton } from "./_components/offer-delivery";
import { OrderDetail } from "./_components/order-detail";

async function getRequests({
  token,
  page = 1,
  limit = 10,
  sort = "",
  minPrice,
  maxPrice,
}: {
  token: string | null;
  page?: number;
  limit?: number;
  sort?: string;
  minPrice?: number;
  maxPrice?: number;
}) {
  const params = new URLSearchParams({
    isAccepted: "false",
    page: page.toString(),
    limit: limit.toString(),
  });

  if (sort) {
    params.append("sort", sort);
  }

  if (minPrice !== undefined && minPrice > 0) {
    params.append("minPrice", minPrice.toString());
  }

  if (maxPrice !== undefined && maxPrice > 0) {
    params.append("maxPrice", maxPrice.toString());
  }

  const response = await axios.get(
    `${routes.api.logisticProvider.deliveryRequests.url()}?${params.toString()}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );

  return response.data;
}

export default function RequestsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const { token } = useAuthContext();

  const currentPage = Number(searchParams.get("page") || "1");
  const currentSort = searchParams.get("sort") || "";
  const currentMinPrice = searchParams.get("minPrice")
    ? Number(searchParams.get("minPrice"))
    : undefined;
  const currentMaxPrice = searchParams.get("maxPrice")
    ? Number(searchParams.get("maxPrice"))
    : undefined;

  const {
    data: requestsQuery,
    isLoading: requestsQueryIsLoading,
    isError: requestsQueryIsError,
  } = useQuery<
    MultipleResponseType<{
      deliveryRequests: (PublicDeliveryRequestType & {
        order: PublicOrderType & {
          orderToProduct: (PublicOrderToProductType & {
            product: PublicProductType & {
              category: PublicCategoryType;
              vendor: VendorProfileType;
            };
          })[];
          user: UserProfileType;
        };
      })[];
    }>
  >({
    queryKey: [
      "requests",
      currentPage,
      currentSort,
      currentMinPrice,
      currentMaxPrice,
    ],
    queryFn: () =>
      getRequests({
        token,
        page: currentPage,
        sort: currentSort,
        minPrice: currentMinPrice,
        maxPrice: currentMaxPrice,
      }),
  });

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(window.location.search);
    params.set("page", page.toString());
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    router.push(newUrl);
  };

  const requests = requestsQuery?.data?.deliveryRequests || [];

  return (
    <PageLayout
      title="Requests"
      description="You can search, filter and manage customer requests from here."
      isLoading={requestsQueryIsLoading}
      isError={requestsQueryIsError}
      errorTitle="Failed to load requests"
      errorDescription="We couldn't load your requests information. Please try again."
    >
      <DataTable
        data={requests}
        columns={[
          {
            header: "Customer",
            cell: (request) => (
              <div className={cn("flex items-center gap-2")}>
                <Avatar className="size-10">
                  <AvatarImage
                    src={`${process.env.NEXT_PUBLIC_FILE_URL}/${request.order.user.pictureId}`}
                    alt={request.order.user.name}
                    className={cn("object-cover")}
                  />
                  <AvatarFallback>
                    {request.order.user.name
                      .split(" ")
                      .map((part) => part.charAt(0).toUpperCase())
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-col text-wrap">
                  <span className="text-sm font-medium">
                    {request.order.user.name}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {request.order.user.phone}
                  </span>
                </div>
              </div>
            ),
          },
          {
            header: "Items",
            cell: (request) => (
              <span>{request.order.orderToProduct.length}</span>
            ),
          },
          {
            header: "Price",
            cell: (request) => <span>{formatPrice(request.price)}</span>,
          },
          {
            header: "Date",
            cell: (request) => <span>{formatDate(request.createdAt)}</span>,
          },
          {
            header: "",
            cell: (request) => (
              <>
                <OfferDeliveryButton deliveryRequestId={request.id} />
                <OrderDetail order={request.order} />
              </>
            ),
          },
        ]}
        currentPage={currentPage}
        meta={requestsQuery?.meta}
        onPageChange={handlePageChange}
        itemName="requests"
        emptyState={{
          icon: ShoppingCartIcon,
          title: "No requests found",
          description: "No requests match your current filters.",
        }}
      />
    </PageLayout>
  );
}
