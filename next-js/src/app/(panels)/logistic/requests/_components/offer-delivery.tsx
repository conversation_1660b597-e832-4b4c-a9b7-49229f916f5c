"use client";

import { useState } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";

import axios, { AxiosError } from "axios";
import { CheckCircle, Loader2Icon } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import * as zod from "zod";

import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { useAuthContext } from "~/context/auth";
import { routes } from "~/lib/routes";
import type {
  LogisticProviderProfileType,
  PublicAuthType,
  PublicLogisticProviderResponseType,
  SingleResponseType,
} from "~/lib/types";
import { cn } from "~/lib/utils";

const DeliveryOfferFormSchema = zod.object({
  price: zod.coerce
    .number({
      required_error: "Price is required",
      invalid_type_error: "Price must be a number",
    })
    .min(1, {
      message: "Price must be at least 1",
    }),
});

async function createDeliveryOffer({
  token,
  deliveryRequestId,
  data,
}: {
  token: string | null;
  deliveryRequestId: string;
  data: zod.infer<typeof DeliveryOfferFormSchema>;
}) {
  const response = await axios.post<
    SingleResponseType<{
      deliveryOffer: PublicLogisticProviderResponseType & {
        logisticProvider: LogisticProviderProfileType & {
          auth: PublicAuthType;
        };
      };
    }>
  >(
    routes.api.logisticProvider.logisticProviderResponses.url(
      deliveryRequestId,
    ),
    data,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );

  return response.data;
}

interface OfferDeliveryButtonProps {
  deliveryRequestId: string;
  disabled?: boolean;
}

export function OfferDeliveryButton({
  deliveryRequestId,
  disabled = false,
}: OfferDeliveryButtonProps) {
  const queryClient = useQueryClient();

  const { token } = useAuthContext();

  const [isOpen, setIsOpen] = useState(false);

  const form = useForm<zod.infer<typeof DeliveryOfferFormSchema>>({
    resolver: zodResolver(DeliveryOfferFormSchema),
    defaultValues: {
      price: 0,
    },
  });

  const createDeliveryOfferMutation = useMutation({
    mutationFn: createDeliveryOffer,
    onSuccess: ({ info }) => {
      toast.success(info.message);
      setIsOpen(false);
      queryClient.invalidateQueries({ queryKey: ["requests"] });
    },
    onError: (error) => {
      if (error instanceof AxiosError) {
        toast.error(
          error.response?.data.info.message ||
            "Failed to create delivery offer",
        );
      } else {
        toast.error("An unexpected error occurred");
      }
    },
  });

  const onSubmit = (data: zod.infer<typeof DeliveryOfferFormSchema>) => {
    createDeliveryOfferMutation.mutate({ token, deliveryRequestId, data });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" disabled={disabled}>
          <CheckCircle className="size-4" />
          <span className="sr-only"> Offer Delivery</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Offer Delivery</DialogTitle>
          <DialogDescription>
            Enter the price you're willing to deliver the order for.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className={cn("space-y-6")}
          >
            <FormField
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Delivery Price</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="Enter price" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              variant="default-gradient"
              size="lg"
              className={cn("w-full")}
              type="submit"
              disabled={createDeliveryOfferMutation.isPending}
            >
              {createDeliveryOfferMutation.isPending && (
                <Loader2Icon className={cn("mr-2 size-4 animate-spin")} />
              )}
              <span>Submit</span>
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
