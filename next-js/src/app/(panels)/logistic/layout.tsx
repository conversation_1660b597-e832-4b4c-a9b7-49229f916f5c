"use client";

import type { PropsWithChildren } from "react";

import { ClipboardListIcon, SettingsIcon, TruckIcon } from "lucide-react";

import { routes } from "~/lib/routes";
import { cn } from "~/lib/utils";
import { DashboardSidebar } from "../_components/dasboard-sidebar";

const items = [
  {
    title: routes.app.logisticProvider.deliveries.label,
    icon: TruckIcon,
    url: routes.app.logisticProvider.deliveries.url(),
  },
  {
    title: routes.app.logisticProvider.requests.label,
    icon: ClipboardListIcon,
    url: routes.app.logisticProvider.requests.url(),
  },
  {
    title: routes.app.logisticProvider.settings.label,
    icon: SettingsIcon,
    url: routes.app.logisticProvider.settings.url(),
  },
];

export default function DashboardLayout({
  children,
}: Readonly<PropsWithChildren>) {
  return (
    <main className={cn("py-7 px-7 min-h-svh w-full flex")}>
      <DashboardSidebar items={items} />
      {children}
    </main>
  );
}
