import type { MultipleResponseType } from "~/lib/types";

import { TablePagination } from "~/components/table/table-pagination";
import { CardDescription, CardFooter } from "~/components/ui/card";
import { cn } from "~/lib/utils";

interface TableFooterProps {
  currentPage: number;
  meta?: Partial<MultipleResponseType<unknown>["meta"]>;
  onPageChange: (page: number) => void;
  itemName?: string;
  className?: string;
}

export function TableFooter({
  currentPage,
  meta,
  onPageChange,
  itemName = "items",
  className,
}: TableFooterProps) {
  return (
    <CardFooter className={cn("flex items-center gap-8", className)}>
      <CardDescription>
        <p>
          Showing{" "}
          {(meta?.limit || 0) < (meta?.total || 0)
            ? meta?.limit || 0
            : meta?.total || 0}{" "}
          of {meta?.total || 0} {itemName}
        </p>
      </CardDescription>
      <TablePagination
        currentPage={currentPage}
        meta={meta}
        onPageChange={onPageChange}
      />
    </CardFooter>
  );
}
