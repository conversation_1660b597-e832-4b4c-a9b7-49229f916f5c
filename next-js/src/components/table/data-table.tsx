import type { LucideIcon } from "lucide-react";
import type { ReactNode } from "react";
import type { MultipleResponseType } from "~/lib/types";

import { PackageIcon } from "lucide-react";
import { EmptyState } from "~/components/layout/empty-state";
import { Card, CardContent } from "~/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { cn } from "~/lib/utils";
import { TableFooter } from "./table-footer";

interface DataTableProps<T> {
  data: T[] | undefined;
  columns: {
    header: string;
    cell: (item: T) => ReactNode;
  }[];
  currentPage: number;
  meta?: Partial<MultipleResponseType<unknown>["meta"]>;
  onPageChange: (page: number) => void;
  itemName: string;
  emptyState?: {
    title?: string;
    description?: string;
    icon?: LucideIcon;
    action?: {
      label: string;
      onClick: () => void;
    };
  };
}

export function DataTable<T>({
  data,
  columns,
  currentPage,
  meta,
  onPageChange,
  itemName,
  emptyState = {
    title: "No items found",
    description:
      "Try adjusting your search or filter to find what you're looking for.",
    icon: PackageIcon,
  },
}: DataTableProps<T>) {
  const isEmpty = !data || data.length === 0;

  return (
    <Card>
      <CardContent className={cn(isEmpty ? "p-6" : "p-0")}>
        {isEmpty ? (
          <EmptyState
            icon={emptyState.icon || PackageIcon}
            title={emptyState.title || "No items found"}
            description={
              emptyState.description ||
              "Try adjusting your search or filter to find what you're looking for."
            }
            action={emptyState.action}
            className="w-full"
          />
        ) : (
          <div className="overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  {columns.map((column, index) => (
                    // biome-ignore lint/suspicious/noArrayIndexKey:
                    <TableHead key={index} className="px-4 py-3">
                      {column.header}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.map((item, rowIndex) => (
                  // biome-ignore lint/suspicious/noArrayIndexKey:
                  <TableRow key={rowIndex}>
                    {columns.map((column, colIndex) => (
                      // biome-ignore lint/suspicious/noArrayIndexKey:
                      <TableCell key={colIndex} className="px-4 py-3">
                        {column.cell(item)}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
      {!isEmpty && (
        <TableFooter
          currentPage={currentPage}
          meta={meta}
          onPageChange={onPageChange}
          itemName={itemName}
        />
      )}
    </Card>
  );
}
