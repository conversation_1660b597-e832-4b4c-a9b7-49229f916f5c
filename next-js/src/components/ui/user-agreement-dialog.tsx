"use client";

import { But<PERSON> } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import { ScrollArea } from "~/components/ui/scroll-area";
import { AgreementContent } from "~/components/ui/agreement-content";
import { Role } from "~/lib/types";

interface UserAgreementDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAgree: () => void;
  role?: Role;
}

export function UserAgreementDialog({
  isOpen,
  onClose,
  onAgree,
  role = Role.USER,
}: UserAgreementDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>EcoBuiltConnect Marketplace Agreement</DialogTitle>
          <DialogDescription>
            Please read and accept the terms before proceeding.
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="h-[300px] w-full rounded-md border p-4">
          <AgreementContent role={role} />
        </ScrollArea>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Decline
          </Button>
          <Button variant="default-gradient" onClick={onAgree}>
            Accept & Continue
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
