"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";

import axios, { AxiosError } from "axios";
import { Loader2Icon } from "lucide-react";
import { useForm } from "react-hook-form";
import * as zod from "zod";

import { toast } from "sonner";
import { Button } from "~/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { Textarea } from "~/components/ui/textarea";
import { useAuthContext } from "~/context/auth";
import { routes } from "~/lib/routes";
import { cn } from "~/lib/utils";

const ContactFormSchema = zod.object({
  name: zod.string({
    message: "Name must be a string",
  }),
  email: zod
    .string({
      message: "Email must be a string",
    })
    .email({
      message: "Invalid email",
    }),
  phone: zod.string({
    message: "Phone must be a string",
  }),
  subject: zod.string({
    message: "Subject must be a string",
  }),
  message: zod.string({
    message: "Message must be a string",
  }),
});

async function contact({
  name,
  email,
  phone,
  subject,
  message,
}: zod.infer<typeof ContactFormSchema>) {
  const response = await axios.post(routes.api.public.contact.url(), {
    name,
    email,
    phone,
    subject,
    message,
  });

  return response.data;
}

export function ContactForm() {
  const { auth } = useAuthContext();

  const form = useForm<zod.infer<typeof ContactFormSchema>>({
    resolver: zodResolver(ContactFormSchema),
    defaultValues: {
      name: "",
      email: auth ? auth.email : "",
      phone: "",
      subject: "",
      message: "",
    },
  });

  const contactMutation = useMutation({
    mutationFn: contact,
    onSuccess: ({ info }) => {
      toast.success(info.message);
    },
    onError: (error) => {
      if (error instanceof AxiosError) {
        toast.error(error.response?.data.info.message);
      }
    },
    onSettled: () => {
      form.reset();
    },
  });

  const onSubmit = (data: zod.infer<typeof ContactFormSchema>) => {
    contactMutation.mutate(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className={cn("space-y-6")}>
        <div className={cn("flex gap-2 items-start")}>
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem className={cn("flex-1")}>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="John Doe" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem className={cn("flex-1")}>
                <FormLabel>Phone</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="************" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className={cn("flex gap-2 items-start")}>
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem className={cn("flex-1")}>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    disabled={!!auth}
                    type="text"
                    placeholder="<EMAIL>"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className={cn("flex gap-2 items-start")}>
          <FormField
            control={form.control}
            name="subject"
            render={({ field }) => (
              <FormItem className={cn("flex-1")}>
                <FormLabel>Subject</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Your Subject" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className={cn("flex gap-2 items-start")}>
          <FormField
            control={form.control}
            name="subject"
            render={({ field }) => (
              <FormItem className={cn("flex-1")}>
                <FormLabel>Message</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Your Message"
                    {...field}
                    className={cn("resize-none")}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div>
          <Button
            variant="default-gradient"
            size="lg"
            className={cn("w-full")}
            type="submit"
            disabled={contactMutation.isPending}
          >
            {contactMutation.isPending && (
              <Loader2Icon className={cn("animate-spin")} />
            )}
            <span>Send Message</span>
          </Button>
        </div>
      </form>
    </Form>
  );
}
