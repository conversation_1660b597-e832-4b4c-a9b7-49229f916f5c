import type {
  CartItemType,
  PublicCategoryType,
  PublicProductType,
  VendorProfileType,
} from "~/lib/types";

/**
 * Checks if a product being added to the cart is from the same vendor as existing products
 * @param cartItems Current items in the cart
 * @param newProduct Product being added to the cart
 * @returns Object containing vendor compatibility information
 */
export function checkVendorCompatibility(
  cartItems: (CartItemType & {
    category: PublicCategoryType;
    vendor: VendorProfileType;
  })[],
  newProduct: PublicProductType & { vendor: VendorProfileType },
) {
  if (cartItems.length === 0) {
    return {
      isSameVendor: true,
      currentVendor: null,
      newVendor: newProduct.vendor,
    };
  }

  const firstItemWithVendor = cartItems.find((item) => item.vendor.id);

  if (!firstItemWithVendor || !firstItemWithVendor.vendor) {
    return {
      isSameVendor: true,
      currentVendor: null,
      newVendor: newProduct.vendor,
    };
  }

  const currentVendor = firstItemWithVendor.vendor;
  const isSameVendor = currentVendor.id === newProduct.vendor.id;

  return {
    isSameVendor,
    currentVendor,
    newVendor: newProduct.vendor,
  };
}
