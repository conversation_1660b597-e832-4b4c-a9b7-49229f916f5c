import { useCallback, useEffect, useState } from "react";

import type { Socket } from "socket.io-client";

import { socketClient } from "~/lib/socket";

export const useSocket = (token: string | null) => {
  const [socket, setSocket] = useState<Socket | null>(null);

  const connect = useCallback(() => {
    if (!token) return;

    const newSocket = socketClient.connect(token);

    setSocket(newSocket || null);

    return newSocket;
  }, [token]);

  const disconnect = useCallback(() => {
    socketClient.disconnect();

    setSocket(null);
  }, []);

  useEffect(() => {
    if (token) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [token, connect, disconnect]);

  return {
    socket,
    connect,
    disconnect,
  };
};
