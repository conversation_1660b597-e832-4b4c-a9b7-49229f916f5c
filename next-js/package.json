{"name": "@ecobuilt/next-js", "version": "0.1.0", "private": true, "scripts": {"biome:only": "biome check", "biome:safe": "biome check --write", "biome:unsafe": "biome check --write --unsafe", "ui": "shadcn", "dev": "next dev --turbopack", "build": "next build", "start": "next start"}, "packageManager": "pnpm@10.10.0", "dependencies": {"@hookform/resolvers": "^4.1.3", "@icons-pack/react-simple-icons": "^13.3.0", "@nanostores/persistent": "^0.10.2", "@nanostores/react": "^0.8.4", "@paystack/inline-js": "^2.22.6", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.81.5", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "lucide-react": "^0.482.0", "nanostores": "^0.11.4", "next": "15.2.2", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.59.0", "socket.io-client": "^4.8.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.71"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20.19.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "shadcn": "2.4.0-canary.14", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}}