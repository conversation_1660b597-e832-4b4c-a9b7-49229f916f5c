{"scripts": {"clean:node": "cd node-js && rm -rf node_modules", "clean:next": "cd next-js && rm -rf node_modules .next", "clone:node": "gh repo clone shayanameend/ecobuilt-node-js node-js", "clone:next": "gh repo clone shayanameend/ecobuilt-next-js next-js", "setup:node": "cp node.env node-js/.env", "setup:next": "cp next.env next-js/.env", "reset:node": "rm -rf node-js && pnpm clone:node && pnpm setup:node", "reset:next": "rm -rf next-js && pnpm clone:next && pnpm setup:next", "dev:node": "cd node-js && pnpm i && pnpm dev", "dev:next": "cd next-js && pnpm i && pnpm dev", "biome:node": "cd node-js && pnpm biome:safe", "biome:next": "cd next-js && pnpm biome:safe", "prisma:studio": "cd node-js && pnpm prisma:studio"}, "packageManager": "pnpm@10.11.1"}