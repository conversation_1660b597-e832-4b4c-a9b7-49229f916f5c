import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  // 1) Drop the unique index on old_name
  await prisma.$runCommandRaw({
    dropIndexes: "Collection",
    index: "Collection_old_name_key",
  });
  console.log("Dropped index Collection_old_name_key");

  // 2) Rename only those docs where old_name != null
  const renameRes = await prisma.$runCommandRaw({
    update: "Collection",
    updates: [
      {
        q: { old_name: { $exists: true, $ne: null } },
        u: { $rename: { old_name: "new_name" } },
        multi: true,
      },
    ],
  });
  console.log("Renamed fields:", renameRes);

  // 3) Recreate a unique index on new_name
  await prisma.$runCommandRaw({
    createIndexes: "Collection",
    indexes: [
      {
        key: { new_name: 1 },
        name: "Collection_new_name_key",
        unique: true,
        // optional: sparse:true if you want to allow missing new_name
      },
    ],
  });
  console.log("Created unique index on new_name");
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(() => prisma.$disconnect());
