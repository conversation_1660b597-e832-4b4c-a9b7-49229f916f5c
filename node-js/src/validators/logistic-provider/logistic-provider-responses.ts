import * as zod from "zod";

const createLogisticProviderResponsesParamsSchema = zod.object({
  deliveryRequestId: zod
    .string({
      message: "Delivery Request ID must be a string",
    })
    .length(24, {
      message: "Delivery Request ID must be a 24-character string",
    }),
});

const createLogisticProviderResponsesBodySchema = zod.object({
  price: zod
    .number({
      message: "Price must be a number",
    })
    .min(1, {
      message: "Price must be a positive number",
    }),
});

export {
  createLogisticProviderResponsesParamsSchema,
  createLogisticProviderResponsesBodySchema,
};
