import { DeliveryRequestStatus } from "@prisma/client";
import * as zod from "zod";

const getDeliveryRequestsQuerySchema = zod.object({
  page: zod.coerce
    .number({
      message: "Page must be a number",
    })
    .int({
      message: "Page must be an integer",
    })
    .min(1, {
      message: "Page must be a positive number",
    })
    .default(1),
  limit: zod.coerce
    .number({
      message: "Limit must be a number",
    })
    .int({
      message: "Limit must be an integer",
    })
    .min(1, {
      message: "Limit must be a positive number",
    })
    .default(10),
  sort: zod
    .enum(["LATEST", "OLDEST"], {
      message: "Sort must be one of 'LATEST', 'OLDEST'",
    })
    .default("LATEST"),
  minPrice: zod.coerce
    .number({
      message: "Min Price must be a number",
    })
    .min(1, {
      message: "Min Price must be a positive number",
    })
    .optional(),
  maxPrice: zod.coerce
    .number({
      message: "Max Price must be a number",
    })
    .min(1, {
      message: "Max Price must be a positive number",
    })
    .optional(),
  isAccepted: zod
    .preprocess(
      (val) => (val === "true" ? true : val === "false" ? false : val),
      zod.boolean({
        message: "isAccepted must be a boolean",
      }),
    )
    .default(false),
});

const toggleDeliveryRequestStatusParamsSchema = zod.object({
  id: zod
    .string({
      message: "ID must be a string",
    })
    .length(24, {
      message: "ID must be a 24-character string",
    }),
});

const toggleDeliveryRequestStatusBodySchema = zod.object({
  status: zod.enum(
    [DeliveryRequestStatus.IN_TRANSIT, DeliveryRequestStatus.DELIVERED],
    {
      message: "Status must be one of 'IN_TRANSIT', 'DELIVERED'",
    },
  ),
});

export {
  getDeliveryRequestsQuerySchema,
  toggleDeliveryRequestStatusParamsSchema,
  toggleDeliveryRequestStatusBodySchema,
};
