import * as zod from "zod";

const addItemToCartBodySchema = zod.object({
  productId: zod
    .string({
      message: "Product ID must be a string",
    })
    .length(24, {
      message: "Product ID must be a 24-character string",
    }),
  quantity: zod
    .number({
      message: "Quantity must be a number",
    })
    .int({
      message: "Quantity must be an integer",
    })
    .min(1, {
      message: "Quantity must be a positive number",
    }),
  replace: zod
    .boolean({
      message: "Replace must be a boolean",
    })
    .default(false),
});

const updateCartItemBodySchema = zod.object({
  quantity: zod
    .number({
      message: "Quantity must be a number",
    })
    .int({
      message: "Quantity must be an integer",
    })
    .min(0, {
      message: "Quantity must be a non-negative number",
    }),
});

const removeItemFromCartParamsSchema = zod.object({
  productId: zod
    .string({
      message: "Product ID must be a string",
    })
    .length(24, {
      message: "Product ID must be a 24-character string",
    }),
});

export {
  addItemToCartBodySchema,
  updateCartItemBodySchema,
  removeItemFromCartParamsSchema,
};
