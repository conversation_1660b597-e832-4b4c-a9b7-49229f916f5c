import * as zod from "zod";

const getDeliveryRequestsQuerySchema = zod.object({
  page: zod.coerce
    .number({
      message: "Page must be a number",
    })
    .int({
      message: "Page must be an integer",
    })
    .min(1, {
      message: "Page must be a positive number",
    })
    .default(1),
  limit: zod.coerce
    .number({
      message: "Limit must be a number",
    })
    .int({
      message: "Limit must be an integer",
    })
    .min(1, {
      message: "Limit must be a positive number",
    })
    .default(10),
  sort: zod
    .enum(["LATEST", "OLDEST"], {
      message: "Sort must be one of 'LATEST', 'OLDEST'",
    })
    .default("LATEST"),
  minPrice: zod.coerce
    .number({
      message: "Min Price must be a number",
    })
    .min(1, {
      message: "Min Price must be a positive number",
    })
    .optional(),
  maxPrice: zod.coerce
    .number({
      message: "Max Price must be a number",
    })
    .min(1, {
      message: "Max Price must be a positive number",
    })
    .optional(),
  logisticProviderId: zod
    .string({
      message: "Logistic Provider ID must be a string",
    })
    .length(24, {
      message: "Logistic Provider ID must be a 24-character string",
    })
    .optional(),
});

const getDeliveryRequestParamsSchema = zod.object({
  orderId: zod
    .string({
      message: "Order ID must be a string",
    })
    .length(24, {
      message: "Order ID must be a 24-character string",
    }),
});

const createDeliveryRequestParamsSchema = zod.object({
  orderId: zod
    .string({
      message: "Order ID must be a string",
    })
    .length(24, {
      message: "Order ID must be a 24-character string",
    }),
});

const createDeliveryRequestBodySchema = zod.object({
  price: zod
    .number({
      message: "Price must be a number",
    })
    .min(1, {
      message: "Price must be a positive number",
    }),
});

export {
  getDeliveryRequestsQuerySchema,
  getDeliveryRequestParamsSchema,
  createDeliveryRequestParamsSchema,
  createDeliveryRequestBodySchema,
};
