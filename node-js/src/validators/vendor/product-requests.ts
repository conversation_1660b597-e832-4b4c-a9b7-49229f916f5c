import * as zod from "zod";

const getProductRequestsQuerySchema = zod.object({
  page: zod.coerce
    .number({
      message: "Page must be a number",
    })
    .int({
      message: "Page must be an integer",
    })
    .min(1, {
      message: "Page must be a positive number",
    })
    .default(1),
  limit: zod.coerce
    .number({
      message: "Limit must be a number",
    })
    .int({
      message: "Limit must be an integer",
    })
    .min(1, {
      message: "Limit must be a positive number",
    })
    .default(10),
  sort: zod
    .enum(["LATEST", "OLDEST"], {
      message: "Sort must be one of 'LATEST', 'OLDEST'",
    })
    .default("LATEST"),
  name: zod
    .string({
      message: "Name must be a string",
    })
    .min(1, {
      message: "Name must be at least 1 characters long",
    })
    .optional(),
  minQuantity: zod.coerce
    .number({
      message: "Quantity must be a number",
    })
    .int({
      message: "Quantity must be an integer",
    })
    .min(1, {
      message: "Quantity must be a positive number",
    })
    .optional(),
  minPrice: zod.coerce
    .number({
      message: "Min Price must be a number",
    })
    .min(1, {
      message: "Min Price must be a positive number",
    })
    .optional(),
  maxPrice: zod.coerce
    .number({
      message: "Max Price must be a number",
    })
    .min(1, {
      message: "Max Price must be a positive number",
    })
    .optional(),
  categoryId: zod
    .string({
      message: "Category ID must be a string",
    })
    .length(24, {
      message: "Category ID must be a 24-character string",
    })
    .optional(),
});

export { getProductRequestsQuerySchema };
