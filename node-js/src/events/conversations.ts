import type {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  GetMessagesBody,
  JoinConversationBody,
  ReadMessagesBody,
  SendMessagesBody,
  SocketEventParams,
} from "~/socket";

import { BadResponse } from "~/lib/error";
import { events } from "~/lib/events";
import { prisma } from "~/lib/prisma";
import { logisticProviderSelector } from "~/selectors/logistic-provider";
import { publicSelector } from "~/selectors/public";
import { userSelector } from "~/selectors/user";
import { vendorSelector } from "~/selectors/vendor";

async function getConversations(
  { io, socket }: SocketEventParams,
  ack?: AckCallback
) {
  const userAuth = socket.request.auth;

  try {
    console.log("Get Conversations for: ", { user: userAuth });

    const conversations = await prisma.conversation.findMany({
      where: {
        members: {
          some: {
            authId: userAuth.id,
          },
        },
      },
      select: {
        ...publicSelector.conversation,
        members: {
          select: {
            auth: {
              select: {
                ...publicSelector.auth,
                user: {
                  select: {
                    ...userSelector.profile,
                  },
                },
                vendor: {
                  select: {
                    ...vendorSelector.profile,
                  },
                },
                logistic: {
                  select: {
                    ...logisticProviderSelector.profile,
                  },
                },
              },
            },
          },
        },
      },
    });

    conversations.forEach((conversation, index) => {
      conversation.members.forEach((member, index) => {
        const { user, vendor, logistic, ...restAuth } = member.auth;
        const parsedMember = {
          auth: {
            ...restAuth,
            profile: user || vendor || logistic,
          },
        };

        // @ts-ignore
        conversation.members[index] = parsedMember;
      });
    });

    socket.join(userAuth.id);

    io.in(userAuth.id).emit(events.conversations.receive, {
      conversations,
    });

    console.log("Conversations: ", conversations);

    if (ack) {
      ack({
        success: true,
        data: { conversations },
      });
    }
  } catch (error) {
    console.error("Error getting conversations:", error);

    if (ack) {
      ack({ success: false, error: "Failed to get conversations" });
    }
  }
}

async function joinConversations(
  {
    io: _io,
    socket,
    type,
    profileId,
    referenceId,
  }: SocketEventParams & JoinConversationBody,
  ack?: AckCallback
) {
  const userAuth = socket.request.auth;

  try {
    console.log("Join Conversation for: ", {
      user: userAuth,
      type,
      profileId,
      referenceId,
    });

    let conversation = await prisma.conversation.findUnique({
      where: {
        referenceId,
      },
      select: {
        ...publicSelector.conversation,
        members: {
          select: {
            auth: {
              select: {
                ...publicSelector.auth,
                user: {
                  select: {
                    ...userSelector.profile,
                  },
                },
                vendor: {
                  select: {
                    ...vendorSelector.profile,
                  },
                },
                logistic: {
                  select: {
                    ...logisticProviderSelector.profile,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!conversation) {
      let memberAuthId: string;

      switch (type) {
        case "LOGISTIC": {
          const logisticProvider = await prisma.logisticProvider.findUnique({
            where: { id: profileId },
            select: {
              ...logisticProviderSelector.profile,
              auth: {
                select: {
                  ...publicSelector.auth,
                },
              },
            },
          });

          if (!logisticProvider) {
            throw new BadResponse("Logistic Provider Not Found!");
          }

          memberAuthId = logisticProvider.auth.id;
          break;
        }
        case "VENDOR": {
          const vendor = await prisma.vendor.findUnique({
            where: { id: profileId },
            select: {
              ...vendorSelector.profile,
              auth: {
                select: {
                  ...publicSelector.auth,
                },
              },
            },
          });

          if (!vendor) {
            throw new BadResponse("Vendor Not Found!");
          }

          memberAuthId = vendor.auth.id;
          break;
        }

        default:
          throw new BadResponse("Invalid Conversation Type");
      }

      console.log("Create Conversation for: ", {
        user: userAuth,
        type,
        memberAuthId,
        profileId,
        referenceId,
      });

      conversation = await prisma.conversation.create({
        data: {
          type,
          referenceId,
        },
        select: {
          ...publicSelector.conversation,
          members: {
            select: {
              auth: {
                select: {
                  ...publicSelector.auth,
                  user: {
                    select: {
                      ...userSelector.profile,
                    },
                  },
                  vendor: {
                    select: {
                      ...vendorSelector.profile,
                    },
                  },
                  logistic: {
                    select: {
                      ...logisticProviderSelector.profile,
                    },
                  },
                },
              },
            },
          },
        },
      });

      await prisma.conversationToAuth.createMany({
        data: [
          {
            conversationId: conversation.id,
            authId: userAuth.id,
          },
          {
            conversationId: conversation.id,
            authId: memberAuthId,
          },
        ],
      });

      // TODO: Send First Message From User (Not Vendor/Logistic)

      conversation.members = await prisma.conversationToAuth.findMany({
        where: {
          conversationId: conversation.id,
        },
        select: {
          auth: {
            select: {
              ...publicSelector.auth,
              user: {
                select: {
                  ...userSelector.profile,
                },
              },
              vendor: {
                select: {
                  ...vendorSelector.profile,
                },
              },
              logistic: {
                select: {
                  ...logisticProviderSelector.profile,
                },
              },
            },
          },
        },
      });
    }

    conversation.members.forEach((member, index) => {
      const { user, vendor, logistic, ...restAuth } = member.auth;
      const parsedMember = {
        auth: {
          ...restAuth,
          profile: user || vendor || logistic,
        },
      };

      // @ts-ignore
      conversation.members[index] = parsedMember;
    });

    socket.join(conversation.id);

    console.log("Conversation: ", conversation);

    if (ack) {
      ack({ success: true, data: { conversation } });
    }
  } catch (error) {
    console.error("Error joining conversation:", error);

    if (ack) {
      ack({ success: false, error: "Failed to join conversation" });
    }
  }
}

async function getMessages(
  { io, socket, conversationId }: SocketEventParams & GetMessagesBody,
  ack?: AckCallback
) {
  const userAuth = socket.request.auth;

  try {
    console.log("Get Messages for: ", { user: userAuth });

    const messages = await prisma.message.findMany({
      where: {
        conversationId,
      },
    });

    io.in(conversationId).emit(events.messages.receive, messages);

    console.log("Messages: ", messages);

    if (ack) {
      ack({ success: true, data: messages });
    }
  } catch (error) {
    console.error("Error getting messages:", error);

    if (ack) {
      ack({ success: false, error: "Failed to get messages" });
    }
  }
}

async function sendMessages(
  { io, socket, conversationId, content }: SocketEventParams & SendMessagesBody,
  ack?: AckCallback
) {
  const userAuth = socket.request.auth;

  try {
    console.log("Send Messages for: ", { user: userAuth });

    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
    });

    if (!conversation) {
      throw new BadResponse("Conversation Not Found!");
    }

    const message = await prisma.message.create({
      data: {
        content,
        sender: {
          connect: { id: userAuth.id },
        },
        conversation: {
          connect: { id: conversation.id },
        },
      },
    });

    io.in(conversationId).emit(events.messages.receive, [message]);

    console.log("Message: ", message);

    if (ack) {
      ack({ success: true, data: message });
    }
  } catch (error) {
    console.error("Error sending message:", error);

    if (ack) {
      ack({ success: false, error: "Failed to send message" });
    }
  }
}

async function readMessages(
  {
    io: _io,
    socket,
    conversationId,
    messageIds,
  }: SocketEventParams & ReadMessagesBody,
  ack?: AckCallback
) {
  const userAuth = socket.request.auth;

  try {
    console.log("Read Messages for: ", { user: userAuth });

    const messages = await prisma.message.updateMany({
      where: {
        id: { in: messageIds },
        senderId: { not: userAuth.id },
        conversationId,
        isRead: false,
      },
      data: {
        isRead: true,
      },
    });

    console.log("Messages: ", messages);

    if (ack) {
      ack({ success: true, data: { count: messages.count } });
    }
  } catch (error) {
    console.error("Error reading messages:", error);

    if (ack) {
      ack({ success: false, error: "Failed to mark messages as read" });
    }
  }
}

export {
  getConversations,
  joinConversations,
  getMessages,
  sendMessages,
  readMessages,
};
