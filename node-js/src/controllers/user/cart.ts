import type { Request, Response } from "express";

import { handleErrors } from "~/lib/error";
import {
  addToCartService,
  clearCartService,
  getOrCreateCartService,
  removeItemFromCartService,
  updateCartItemService,
} from "~/services/user/cart";
import {
  addItemToCartBodySchema,
  removeItemFromCartParamsSchema,
  updateCartItemBodySchema,
} from "~/validators/user/cart";

async function getCartItems(request: Request, response: Response) {
  try {
    const { cart } = await getOrCreateCartService({
      authId: request.auth.id,
    });

    return response.success(
      {
        data: { cart },
      },
      {
        message: "Cart fetched successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function addItemToCart(request: Request, response: Response) {
  try {
    const { productId, quantity, replace } = addItemToCartBodySchema.parse(
      request.body
    );

    const { cartItem } = await addToCartService({
      authId: request.auth.id,
      productId,
      quantity,
      replace,
    });

    return response.success(
      {
        data: { cartItem },
      },
      {
        message: "Item added to cart successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function updateCartItem(request: Request, response: Response) {
  try {
    const { productId } = removeItemFromCartParamsSchema.parse(request.params);
    const { quantity } = updateCartItemBodySchema.parse(request.body);

    const { cartItem, deleted } = await updateCartItemService({
      authId: request.auth.id,
      productId,
      quantity,
    });

    if (deleted) {
      return response.success(
        {
          data: {},
        },
        {
          message: "Item removed from cart successfully",
        }
      );
    }

    return response.success(
      {
        data: { cartItem },
      },
      {
        message: "Cart item updated successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function removeItemFromCart(request: Request, response: Response) {
  try {
    const { productId } = removeItemFromCartParamsSchema.parse(request.params);

    await removeItemFromCartService({
      authId: request.auth.id,
      productId,
    });

    return response.success(
      {
        data: {},
      },
      {
        message: "Item removed from cart successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function clearCartItems(request: Request, response: Response) {
  try {
    await clearCartService({
      authId: request.auth.id,
    });

    return response.success(
      {
        data: {},
      },
      {
        message: "Cart cleared successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

export {
  getCartItems,
  addItemToCart,
  updateCartItem,
  removeItemFromCart,
  clearCartItems,
};
