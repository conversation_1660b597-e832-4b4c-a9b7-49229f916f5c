import type { Request, Response } from "express";

import { handleErrors } from "~/lib/error";
import {
  createOrderService,
  getOrderService,
  getOrdersService,
} from "~/services/user/orders";
import {
  createOrderBodySchema,
  getOrderParamsSchema,
  getOrdersQuerySchema,
} from "~/validators/user/orders";

async function getOrders(request: Request, response: Response) {
  try {
    const {
      page,
      limit,
      sort,
      status,
      productName,
      vendorName,
      minTotalPrice,
      maxTotalPrice,
      categoryId,
    } = getOrdersQuerySchema.parse(request.query);

    const {
      orders,
      total,
      pages,
      limit: responseLimit,
      page: responsePage,
    } = await getOrdersService({
      userId: request.auth.id,
      page,
      limit,
      sort,
      status,
      productName,
      vendorName,
      minTotalPrice,
      maxTotalPrice,
      categoryId,
    });

    return response.success(
      {
        data: { orders },
        meta: { total, pages, limit: responseLimit, page: responsePage },
      },
      {
        message: "Orders fetched successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function getOrder(request: Request, response: Response) {
  try {
    const { id } = getOrderParamsSchema.parse(request.params);

    const { order } = await getOrderService({
      userId: request.auth.id,
      orderId: id,
    });

    return response.success(
      {
        data: { order },
      },
      {
        message: "Order fetched successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function createOrder(request: Request, response: Response) {
  try {
    const { products, deliveryOption } = createOrderBodySchema.parse(
      request.body
    );

    const { order } = await createOrderService({
      userId: request.auth.id,
      products,
      deliveryOption,
    });

    return response.success(
      {
        data: { order },
      },
      {
        message: "Order created successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

export { getOrders, getOrder, createOrder };
