import type { Request, Response } from "express";

import { handleErrors } from "~/lib/error";
import {
  getProfileService,
  updateProfileService,
} from "~/services/user/profile";
import { updateProfileBodySchema } from "~/validators/user/profile";

async function getProfile(request: Request, response: Response) {
  try {
    const { profile } = await getProfileService({
      userId: request.auth.id,
    });

    return response.success(
      {
        data: {
          profile,
        },
      },
      {
        message: "Profile fetched successfully",
      }
    );
  } catch (error) {
    return handleErrors({ response, error });
  }
}

async function updateProfile(request: Request, response: Response) {
  try {
    const { name, phone, postalCode, city, deliveryAddress } =
      updateProfileBodySchema.parse(request.body);

    const { profile } = await updateProfileService({
      userId: request.auth.id,
      name,
      phone,
      postalCode,
      city,
      deliveryAddress,
      pictureId: request.body.pictureId,
      file: request.file,
    });

    return response.success(
      {
        data: {
          profile,
        },
      },
      {
        message: "Profile updated successfully",
      }
    );
  } catch (error) {
    return handleErrors({ response, error });
  }
}

export { getProfile, updateProfile };
