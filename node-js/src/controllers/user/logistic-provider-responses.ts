import type { Request, Response } from "express";

import { handleErrors } from "~/lib/error";
import {
  acceptLogisticProviderResponseService,
  getLogisticProviderResponsesService,
} from "~/services/user/logistic-provider-responses";
import {
  acceptLogisticProviderResponseParamsSchema,
  getLogisticProviderResponsesParamsSchema,
  getLogisticProviderResponsesQuerySchema,
} from "~/validators/user/logistic-provider-responses";

async function getLogisticProviderResponses(
  request: Request,
  response: Response
) {
  try {
    const { deliveryRequestId } =
      getLogisticProviderResponsesParamsSchema.parse(request.params);

    const { page, limit, sort, minPrice, maxPrice } =
      getLogisticProviderResponsesQuerySchema.parse(request.query);

    const {
      responses,
      total,
      pages,
      limit: responseLimit,
      page: responsePage,
    } = await getLogisticProviderResponsesService({
      userId: request.auth.id,
      deliveryRequestId,
      page,
      limit,
      sort,
      minPrice,
      maxPrice,
    });

    return response.success(
      {
        data: { responses },
        meta: { total, pages, limit: responseLimit, page: responsePage },
      },
      {
        message: "Logistic provider responses fetched successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function acceptLogisticProviderResponse(
  request: Request,
  response: Response
) {
  try {
    const { id } = acceptLogisticProviderResponseParamsSchema.parse(
      request.params
    );

    const { response: logisticProviderResponse } =
      await acceptLogisticProviderResponseService({
        userId: request.auth.id,
        responseId: id,
      });

    return response.success(
      {
        data: { response: logisticProviderResponse },
      },
      {
        message: "Logistic provider response accepted successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

export { getLogisticProviderResponses, acceptLogisticProviderResponse };
