import type { Request, Response } from "express";

import { handleErrors } from "~/lib/error";
import {
  getLogisticProviderService,
  getLogisticProvidersService,
  updateLogisticProviderService,
} from "~/services/admin/logistic-providers";
import {
  getLogisticProviderParamsSchema,
  getLogisticProvidersQuerySchema,
  updateLogisticProviderBodySchema,
  updateLogisticProviderParamsSchema,
} from "~/validators/admin/logistic-providers";

async function getLogisticProviders(request: Request, response: Response) {
  try {
    const {
      page,
      limit,
      sort,
      email,
      name,
      phone,
      postalCode,
      city,
      address,
      status,
      isVerified,
      isDeleted,
    } = getLogisticProvidersQuerySchema.parse(request.query);

    const {
      logisticProviders,
      total,
      pages,
      limit: responseLimit,
      page: responsePage,
    } = await getLogisticProvidersService({
      page,
      limit,
      sort,
      email,
      name,
      phone,
      postalCode,
      city,
      address,
      status,
      isVerified,
      isDeleted,
    });

    return response.success(
      {
        data: { logisticProviders },
        meta: { total, pages, limit: responseLimit, page: responsePage },
      },
      {
        message: "Logistic providers fetched successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function getLogisticProvider(request: Request, response: Response) {
  try {
    const { id } = getLogisticProviderParamsSchema.parse(request.params);
    const { logisticProvider } = await getLogisticProviderService({
      logisticProviderId: id,
    });

    return response.success(
      {
        data: { logisticProvider },
      },
      {
        message: "Logistic provider fetched successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function updateLogisticProvider(request: Request, response: Response) {
  try {
    const { id } = updateLogisticProviderParamsSchema.parse(request.params);
    const validatedData = updateLogisticProviderBodySchema.parse(request.body);

    const { logisticProvider } = await updateLogisticProviderService({
      logisticProviderId: id,
      data: validatedData,
    });

    return response.success(
      {
        data: { logisticProvider },
      },
      {
        message: "Logistic provider updated successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

export { getLogisticProviders, getLogisticProvider, updateLogisticProvider };
