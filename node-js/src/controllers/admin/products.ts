import type { Request, Response } from "express";

import { handleErrors } from "~/lib/error";
import {
  getProductService,
  getProductsService,
  toggleProductEcoBuiltVerificationService,
  toggleProductIsDeletedService,
} from "~/services/admin/products";
import {
  getProductParamsSchema,
  getProductsQuerySchema,
  toggleProductEcoBuiltVerificationBodySchema,
  toggleProductEcoBuiltVerificationParamsSchema,
  toggleProductIsDeletedParamsSchema,
  toggleProductIsDeletedQuerySchema,
} from "~/validators/admin/products";

async function getProducts(request: Request, response: Response) {
  try {
    const {
      page,
      limit,
      sort,
      name,
      city,
      minStock,
      minPrice,
      maxPrice,
      isVerified,
      isDeleted,
      categoryId,
      vendorId,
      productRequestId,
    } = getProductsQuerySchema.parse(request.query);

    const {
      products,
      total,
      pages,
      limit: responseLimit,
      page: responsePage,
    } = await getProductsService({
      page,
      limit,
      sort,
      name,
      city,
      minStock,
      minPrice,
      maxPrice,
      isVerified,
      isDeleted,
      categoryId,
      vendorId,
      productRequestId,
    });

    return response.success(
      {
        data: { products },
        meta: { total, pages, limit: responseLimit, page: responsePage },
      },
      {
        message: "Products fetched successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function getProduct(request: Request, response: Response) {
  try {
    const { id } = getProductParamsSchema.parse(request.params);

    const { product } = await getProductService({
      productId: id,
    });

    return response.success(
      {
        data: { product },
      },
      {
        message: "Product fetched successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function toggleProductIsDeleted(request: Request, response: Response) {
  try {
    const { id } = toggleProductIsDeletedParamsSchema.parse(request.params);
    const { isDeleted } = toggleProductIsDeletedQuerySchema.parse(
      request.query,
    );

    const { product } = await toggleProductIsDeletedService({
      productId: id,
      isDeleted,
    });

    return response.success(
      {
        data: { product },
      },
      {
        message: `Product ${
          product.isDeleted ? "deleted" : "restored"
        } successfully!`,
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function toggleProductEcoBuiltVerification(
  request: Request,
  response: Response,
) {
  try {
    const { id } = toggleProductEcoBuiltVerificationParamsSchema.parse(
      request.params,
    );
    const { isVerified } = toggleProductEcoBuiltVerificationBodySchema.parse(
      request.body,
    );

    const { product } = await toggleProductEcoBuiltVerificationService({
      productId: id,
      isVerified,
    });

    return response.success(
      {
        data: { product },
      },
      {
        message: `Product ${
          product.isVerified ? "verified" : "unverified"
        } successfully!`,
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

export {
  getProducts,
  getProduct,
  toggleProductIsDeleted,
  toggleProductEcoBuiltVerification,
};
