import type { Request, Response } from "express";

import crypto from "node:crypto";

import { env } from "~/lib/env";
import { handleErrors } from "~/lib/error";
import { handlePaystackChargeSuccessEvent } from "~/services/public/paystack";

async function handlePaystackWebhook(request: Request, response: Response) {
  try {
    const hash = crypto
      .createHmac("sha512", env.PAYSTACK_SECRET_KEY)
      .update(JSON.stringify(request.body))
      .digest("hex");

    if (hash == request.headers["x-paystack-signature"]) {
      const event = request.body.event;

      switch (event) {
        case "charge.success": {
          await handlePaystackChargeSuccessEvent();
        }
      }

      response.success({}, { message: "Event received" });
    }
  } catch (error) {
    handleErrors({ response, error });
  }
}

export { handlePaystackWebhook };
