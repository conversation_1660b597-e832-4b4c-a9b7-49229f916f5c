import type { Request, Response } from "express";

import { handleErrors } from "~/lib/error";
import { getCategoriesService } from "~/services/public/categories";
import { getCategoriesQuerySchema } from "~/validators/public/categories";

async function getCategories(request: Request, response: Response) {
  try {
    const { name } = getCategoriesQuerySchema.parse(request.query);

    const { categories } = await getCategoriesService({ name });

    return response.success(
      {
        data: { categories },
      },
      {
        message: "Categories fetched successfully",
      },
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

export { getCategories };
