import type { Request, Response } from "express";

import { handleErrors } from "~/lib/error";
import {
  getProfileService,
  updateProfileService,
} from "~/services/logistic-provider/profile";
import { updateProfileBodySchema } from "~/validators/logistic-provider/profile";

async function getProfile(request: Request, response: Response) {
  try {
    const { profile } = await getProfileService({
      userId: request.auth.id,
    });

    return response.success(
      {
        data: {
          profile,
        },
      },
      {
        message: "Profile fetched successfully",
      }
    );
  } catch (error) {
    return handleErrors({ response, error });
  }
}

async function updateProfile(request: Request, response: Response) {
  try {
    const { name, description, phone, postalCode, city, address } =
      updateProfileBodySchema.parse(request.body);

    const { profile } = await updateProfileService({
      userId: request.auth.id,
      name,
      description,
      phone,
      postalCode,
      city,
      address,
      pictureId: request.body.pictureId,
      file: request.file,
    });

    return response.success(
      {
        data: {
          profile,
        },
      },
      {
        message: "Profile updated successfully",
      }
    );
  } catch (error) {
    return handleErrors({ response, error });
  }
}

export { getProfile, updateProfile };
