import type { Request, Response } from "express";

import { handleErrors } from "~/lib/error";
import {
  getDeliveryRequestsService,
  toggleDeliveryRequestStatusService,
} from "~/services/logistic-provider/delivery-requests";
import {
  getDeliveryRequestsQuerySchema,
  toggleDeliveryRequestStatusBodySchema,
  toggleDeliveryRequestStatusParamsSchema,
} from "~/validators/logistic-provider/delivery-requests";

async function getDeliveryRequests(request: Request, response: Response) {
  try {
    const { page, limit, sort, minPrice, maxPrice, isAccepted } =
      getDeliveryRequestsQuerySchema.parse(request.query);

    const {
      deliveryRequests,
      total,
      pages,
      limit: responseLimit,
      page: responsePage,
    } = await getDeliveryRequestsService({
      logisticProviderId: request.auth.id,
      page,
      limit,
      sort,
      minPrice,
      maxPrice,
      isAccepted,
    });

    return response.success(
      {
        data: { deliveryRequests },
        meta: { total, pages, limit: responseLimit, page: responsePage },
      },
      {
        message: "Delivery requests fetched successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

async function toggleDeliveryRequestStatus(
  request: Request,
  response: Response
) {
  try {
    const { id } = toggleDeliveryRequestStatusParamsSchema.parse(
      request.params
    );
    const { status } = toggleDeliveryRequestStatusBodySchema.parse(
      request.body
    );

    const { deliveryRequest } = await toggleDeliveryRequestStatusService({
      logisticProviderId: request.auth.id,
      deliveryRequestId: id,
      status,
    });

    return response.success(
      {
        data: { deliveryRequest },
      },
      {
        message: "Delivery request status updated successfully",
      }
    );
  } catch (error) {
    handleErrors({ response, error });
  }
}

export { getDeliveryRequests, toggleDeliveryRequestStatus };
