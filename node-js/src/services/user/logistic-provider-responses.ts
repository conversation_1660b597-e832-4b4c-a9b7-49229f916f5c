import type { Prisma } from "@prisma/client";

import { BadResponse } from "~/lib/error";
import { prisma } from "~/lib/prisma";
import { logisticProviderSelector } from "~/selectors/logistic-provider";
import { publicSelector } from "~/selectors/public";

/**
 * Get all logistic provider responses for a delivery request
 */
async function getLogisticProviderResponsesService({
  userId,
  deliveryRequestId,
  page,
  limit,
  sort,
  minPrice,
  maxPrice,
}: {
  userId: string;
  deliveryRequestId: string;
  page: number;
  limit: number;
  sort?: "LATEST" | "OLDEST";
  minPrice?: number;
  maxPrice?: number;
}) {
  const user = await prisma.user.findUnique({
    where: { authId: userId },
    select: { id: true },
  });

  if (!user) {
    return {
      responses: [],
      total: 0,
      pages: 1,
      limit,
      page,
    };
  }

  const deliveryRequest = await prisma.deliveryRequest.findUnique({
    where: {
      id: deliveryRequestId,
      order: {
        userId: user.id,
      },
    },
    select: { id: true },
  });

  if (!deliveryRequest) {
    return {
      responses: [],
      total: 0,
      pages: 1,
      limit,
      page,
    };
  }

  const where: Prisma.LogisticProviderResponseWhereInput = {
    deliveryRequestId,
  };

  if (minPrice !== undefined && maxPrice !== undefined) {
    where.price = {
      gte: minPrice,
      lte: maxPrice,
    };
  } else if (minPrice !== undefined) {
    where.price = {
      gte: minPrice,
    };
  } else if (maxPrice !== undefined) {
    where.price = {
      lte: maxPrice,
    };
  }

  const responses = await prisma.logisticProviderResponse.findMany({
    where,
    take: limit,
    skip: (page - 1) * limit,
    orderBy: {
      ...(sort === "LATEST" && { createdAt: "desc" }),
      ...(sort === "OLDEST" && { createdAt: "asc" }),
    },
    select: {
      ...publicSelector.logisticProviderResponse,
      logisticProvider: {
        select: {
          ...logisticProviderSelector.profile,
          auth: {
            select: {
              ...publicSelector.auth,
            },
          },
        },
      },
    },
  });

  const total = await prisma.logisticProviderResponse.count({ where });
  const pages = Math.ceil(total / limit);

  return {
    responses,
    total,
    pages,
    limit,
    page,
  };
}

/**
 * Accept a logistic provider response
 */
async function acceptLogisticProviderResponseService({
  userId,
  responseId,
}: {
  userId: string;
  responseId: string;
}) {
  const user = await prisma.user.findUnique({
    where: { authId: userId },
    select: { id: true },
  });

  if (!user) {
    throw new BadResponse("Failed to accept logistic provider response");
  }

  const response = await prisma.logisticProviderResponse.findUnique({
    where: {
      id: responseId,
      deliveryRequest: {
        order: {
          userId: user.id,
        },
        acceptedLogisticProvider: null,
      },
    },
    select: {
      ...publicSelector.logisticProviderResponse,
      deliveryRequest: {
        select: {
          ...publicSelector.deliveryRequest,
        },
      },
      logisticProvider: {
        select: {
          ...logisticProviderSelector.profile,
          auth: {
            select: {
              ...publicSelector.auth,
            },
          },
        },
      },
    },
  });

  if (!response) {
    throw new BadResponse("Failed to accept logistic provider response");
  }

  await prisma.$transaction(async (tx) => {
    const updated = await tx.deliveryRequest.update({
      where: {
        id: response.deliveryRequest.id,
      },
      data: {
        status: "PROCESSING",
        acceptedPrice: response.price,
        acceptedLogisticProviderId: response.logisticProvider.id,
      },
      select: {
        id: true,
      },
    });

    return updated;
  });

  return { response };
}

export {
  getLogisticProviderResponsesService,
  acceptLogisticProviderResponseService,
};
