import { BadResponse } from "~/lib/error";
import { prisma } from "~/lib/prisma";
import { publicSelector } from "~/selectors/public";
import { userSelector } from "~/selectors/user";
import { vendorSelector } from "~/selectors/vendor";

/**
 * Get or create cart for a user
 */
async function getOrCreateCartService({ authId }: { authId: string }) {
  const user = await prisma.user.findUnique({
    where: { authId: authId },
    select: { id: true },
  });

  if (!user) {
    throw new BadResponse("Failed to get or create cart");
  }

  let cart = await prisma.cart.findUnique({
    where: { userId: user.id },
    include: {
      items: {
        select: {
          ...userSelector.cartItem,
          product: {
            select: {
              ...publicSelector.product,
              category: {
                select: publicSelector.category,
              },
              vendor: {
                select: vendorSelector.profile,
              },
            },
          },
        },
        orderBy: { createdAt: "desc" },
      },
    },
  });

  if (!cart) {
    cart = await prisma.cart.create({
      data: { userId: user.id },
      include: {
        items: {
          select: {
            ...userSelector.cartItem,
            product: {
              select: {
                ...publicSelector.product,
                category: {
                  select: publicSelector.category,
                },
                vendor: {
                  select: vendorSelector.profile,
                },
              },
            },
          },
          orderBy: { createdAt: "desc" },
        },
      },
    });
  }

  return { cart };
}

/**
 * Add item to cart
 */
async function addToCartService({
  authId,
  productId,
  quantity,
  replace = false,
}: {
  authId: string;
  productId: string;
  quantity: number;
  replace: boolean;
}) {
  const user = await prisma.user.findUnique({
    where: { authId: authId },
    select: { id: true },
  });

  if (!user) {
    throw new BadResponse("Failed to add item to cart");
  }

  const product = await prisma.product.findUnique({
    where: { id: productId, isDeleted: false },
    select: {
      ...publicSelector.product,
      category: {
        select: publicSelector.category,
      },
      vendor: {
        select: vendorSelector.profile,
      },
    },
  });

  if (!product) {
    throw new BadResponse("Failed to add item to cart");
  }

  if (product.stock < quantity) {
    throw new BadResponse("Quantity exceeds available stock");
  }

  const { cart } = await getOrCreateCartService({ authId: authId });

  if (!replace && cart.items.length > 0) {
    const existingVendorId = cart.items[0]?.product.vendor.id;

    if (existingVendorId && existingVendorId !== product.vendor.id) {
      throw new BadResponse("Failed to add item to cart");
    }
  }

  if (replace) {
    await prisma.cartItem.deleteMany({
      where: { cartId: cart.id },
    });
  }

  const existingItem = await prisma.cartItem.findUnique({
    where: {
      cartId_productId: {
        cartId: cart.id,
        productId,
      },
    },
  });

  let cartItem;

  if (existingItem) {
    const newQuantity = replace ? quantity : existingItem.quantity + quantity;

    if (newQuantity > product.stock) {
      throw new BadResponse("Quantity exceeds available stock");
    }

    cartItem = await prisma.cartItem.update({
      where: { id: existingItem.id },
      data: { quantity: newQuantity },
      select: {
        ...userSelector.cartItem,
        product: {
          select: {
            ...publicSelector.product,
            category: {
              select: publicSelector.category,
            },
            vendor: {
              select: vendorSelector.profile,
            },
          },
        },
      },
    });
  } else {
    cartItem = await prisma.cartItem.create({
      data: {
        cartId: cart.id,
        productId,
        quantity,
      },
      select: {
        ...userSelector.cartItem,
        product: {
          select: {
            ...publicSelector.product,
            category: {
              select: publicSelector.category,
            },
            vendor: {
              select: vendorSelector.profile,
            },
          },
        },
      },
    });
  }

  return { cartItem };
}

/**
 * Update cart item quantity
 */
async function updateCartItemService({
  authId,
  productId,
  quantity,
}: {
  authId: string;
  productId: string;
  quantity: number;
}) {
  const user = await prisma.user.findUnique({
    where: { authId: authId },
    select: { id: true },
  });

  if (!user) {
    throw new BadResponse("Failed to update cart item");
  }

  const cart = await prisma.cart.findUnique({
    where: { userId: user.id },
    select: { id: true },
  });

  if (!cart) {
    throw new BadResponse("Failed to update cart item");
  }

  const cartItem = await prisma.cartItem.findUnique({
    where: {
      cartId_productId: {
        cartId: cart.id,
        productId,
      },
    },
    select: {
      ...userSelector.cartItem,
      product: {
        select: {
          ...publicSelector.product,
        },
      },
    },
  });

  if (!cartItem) {
    throw new BadResponse("Failed to update cart item");
  }

  if (quantity === 0) {
    await prisma.cartItem.delete({
      where: { id: cartItem.id },
    });

    return { cartItem: null, deleted: true };
  }

  if (quantity > cartItem.product.stock) {
    throw new BadResponse("Quantity exceeds available stock");
  }

  const updatedCartItem = await prisma.cartItem.update({
    where: { id: cartItem.id },
    data: { quantity },
    select: {
      ...userSelector.cartItem,
      product: {
        select: {
          ...publicSelector.product,
          category: {
            select: publicSelector.category,
          },
          vendor: {
            select: vendorSelector.profile,
          },
        },
      },
    },
  });

  return { cartItem: updatedCartItem, deleted: false };
}

/**
 * Remove item from cart
 */
async function removeItemFromCartService({
  authId,
  productId,
}: {
  authId: string;
  productId: string;
}) {
  const user = await prisma.user.findUnique({
    where: { authId: authId },
    select: { id: true },
  });

  if (!user) {
    throw new BadResponse("Failed to remove item from cart");
  }

  const cart = await prisma.cart.findUnique({
    where: { userId: user.id },
    select: { id: true },
  });

  if (!cart) {
    throw new BadResponse("Failed to remove item from cart");
  }

  const cartItem = await prisma.cartItem.findUnique({
    where: {
      cartId_productId: {
        cartId: cart.id,
        productId,
      },
    },
  });

  if (!cartItem) {
    throw new BadResponse("Failed to remove item from cart");
  }

  await prisma.cartItem.delete({
    where: { id: cartItem.id },
  });

  return;
}

/**
 * Clear all items from cart
 */
async function clearCartService({ authId }: { authId: string }) {
  const user = await prisma.user.findUnique({
    where: { authId: authId },
    select: { id: true },
  });

  if (!user) {
    throw new BadResponse("Failed to clear cart");
  }

  const cart = await prisma.cart.findUnique({
    where: { userId: user.id },
    select: { id: true },
  });

  if (!cart) {
    throw new BadResponse("Failed to clear cart");
  }

  await prisma.cartItem.deleteMany({
    where: { cartId: cart.id },
  });

  return;
}

export {
  getOrCreateCartService,
  addToCartService,
  updateCartItemService,
  removeItemFromCartService,
  clearCartService,
};
