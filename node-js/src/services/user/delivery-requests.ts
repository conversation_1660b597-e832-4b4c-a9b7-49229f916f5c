import type { Prisma } from "@prisma/client";

import { BadResponse, NotFoundResponse } from "~/lib/error";
import { prisma } from "~/lib/prisma";
import { logisticProviderSelector } from "~/selectors/logistic-provider";
import { publicSelector } from "~/selectors/public";
import { vendorSelector } from "~/selectors/vendor";

/**
 * Get all delivery requests for a user
 */
async function getDeliveryRequestsService({
  userId,
  page,
  limit,
  sort,
  minPrice,
  maxPrice,
  logisticProviderId,
}: {
  userId: string;
  page: number;
  limit: number;
  sort?: "LATEST" | "OLDEST";
  minPrice?: number;
  maxPrice?: number;
  logisticProviderId?: string;
}) {
  const user = await prisma.user.findUnique({
    where: { authId: userId },
    select: { id: true },
  });

  if (!user) {
    return {
      deliveryRequests: [],
      total: 0,
      pages: 1,
      limit,
      page,
    };
  }

  const where: Prisma.DeliveryRequestWhereInput = {
    order: {
      userId: user.id,
    },
  };

  if (minPrice !== undefined && maxPrice !== undefined) {
    where.price = {
      gte: minPrice,
      lte: maxPrice,
    };
  } else if (minPrice !== undefined) {
    where.price = {
      gte: minPrice,
    };
  } else if (maxPrice !== undefined) {
    where.price = {
      lte: maxPrice,
    };
  }

  if (logisticProviderId) {
    where.acceptedLogisticProviderId = logisticProviderId;
  }

  const deliveryRequests = await prisma.deliveryRequest.findMany({
    where,
    take: limit,
    skip: (page - 1) * limit,
    orderBy: {
      ...(sort === "LATEST" && { createdAt: "desc" }),
      ...(sort === "OLDEST" && { createdAt: "asc" }),
    },
    select: {
      ...publicSelector.deliveryRequest,
      order: {
        select: {
          ...publicSelector.order,
          orderToProduct: {
            select: {
              ...publicSelector.orderToProduct,
              product: {
                select: {
                  ...publicSelector.product,
                  category: {
                    select: {
                      ...publicSelector.category,
                    },
                  },
                  vendor: {
                    select: {
                      ...vendorSelector.profile,
                    },
                  },
                },
              },
            },
          },
        },
      },
      acceptedLogisticProvider: {
        select: {
          ...logisticProviderSelector.profile,
        },
      },
    },
  });

  const total = await prisma.deliveryRequest.count({ where });
  const pages = Math.ceil(total / limit);

  return {
    deliveryRequests,
    total,
    pages,
    limit,
    page,
  };
}

/**
 * Get a single delivery request for a user
 */
async function getDeliveryRequestService({
  userId,
  orderId,
}: {
  userId: string;
  orderId: string;
}) {
  const user = await prisma.user.findUnique({
    where: { authId: userId },
    select: { id: true },
  });

  if (!user) {
    throw new NotFoundResponse("Delivery request not found");
  }

  const deliveryRequest = await prisma.deliveryRequest.findUnique({
    where: {
      orderId,
      order: {
        userId: user.id,
      },
    },
    select: {
      ...publicSelector.deliveryRequest,
      order: {
        select: {
          ...publicSelector.order,
          orderToProduct: {
            select: {
              ...publicSelector.orderToProduct,
              product: {
                select: {
                  ...publicSelector.product,
                  category: {
                    select: {
                      ...publicSelector.category,
                    },
                  },
                  vendor: {
                    select: {
                      ...vendorSelector.profile,
                    },
                  },
                },
              },
            },
          },
        },
      },
      acceptedLogisticProvider: {
        select: {
          ...logisticProviderSelector.profile,
        },
      },
    },
  });

  if (!deliveryRequest) {
    throw new NotFoundResponse("Delivery request not found");
  }

  return { deliveryRequest };
}

/**
 * Create a new delivery request for a user
 */
async function createDeliveryRequestService({
  userId,
  orderId,
  price,
}: {
  userId: string;
  orderId: string;
  price: number;
}) {
  const user = await prisma.user.findUnique({
    where: { authId: userId },
    select: { id: true },
  });

  if (!user) {
    throw new NotFoundResponse("Failed to create delivery request");
  }

  const order = await prisma.order.findUnique({
    where: {
      id: orderId,
      userId: user.id,
      deliveryOption: "LOGISTIC",
      deliveryRequest: null,
    },
    select: {
      id: true,
    },
  });

  if (!order) {
    throw new BadResponse("Failed to create delivery request");
  }

  const deliveryRequest = await prisma.deliveryRequest.create({
    data: {
      price,
      order: {
        connect: {
          id: orderId,
        },
      },
    },
    select: {
      ...publicSelector.deliveryRequest,
      order: {
        select: {
          ...publicSelector.order,
          orderToProduct: {
            select: {
              ...publicSelector.orderToProduct,
              product: {
                select: {
                  ...publicSelector.product,
                  category: {
                    select: {
                      ...publicSelector.category,
                    },
                  },
                  vendor: {
                    select: {
                      ...vendorSelector.profile,
                    },
                  },
                },
              },
            },
          },
        },
      },
      acceptedLogisticProvider: {
        select: {
          ...logisticProviderSelector.profile,
        },
      },
    },
  });

  return { deliveryRequest };
}

export {
  getDeliveryRequestsService,
  getDeliveryRequestService,
  createDeliveryRequestService,
};
