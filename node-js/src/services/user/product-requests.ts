import type { Prisma } from "@prisma/client";
import type * as zod from "zod";

import { BadResponse, NotFoundResponse } from "~/lib/error";
import { prisma } from "~/lib/prisma";
import { publicSelector } from "~/selectors/public";
import { userSelector } from "~/selectors/user";
import { addFile } from "~/utils/file";
import type { createProductRequestBodySchema } from "~/validators/user/product-requests";

/**
 * Get all product requests for a user
 */
async function getProductRequestsService({
  userId,
  page,
  limit,
  sort,
  name,
  minQuantity,
  minPrice,
  maxPrice,
  categoryId,
}: {
  userId: string;
  page: number;
  limit: number;
  sort?: "LATEST" | "OLDEST";
  name?: string;
  minQuantity?: number;
  minPrice?: number;
  maxPrice?: number;
  categoryId?: string;
}) {
  const user = await prisma.user.findUnique({
    where: { authId: userId },
    select: { id: true },
  });

  if (!user) {
    return {
      productRequests: [],
      total: 0,
      pages: 1,
      limit,
      page,
    };
  }

  if (categoryId) {
    const category = await prisma.category.findUnique({
      where: { id: categoryId, status: "APPROVED", isDeleted: false },
      select: { id: true },
    });

    if (!category) {
      return {
        productRequests: [],
        total: 0,
        pages: 1,
        limit,
        page,
      };
    }
  }

  const where: Prisma.ProductRequestWhereInput = {
    userId: user.id,
    isDeleted: false,
  };

  if (name) {
    where.name = {
      contains: name,
      mode: "insensitive",
    };
  }

  if (minQuantity !== undefined) {
    where.quantity = {
      gte: minQuantity,
    };
  }

  if (minPrice !== undefined) {
    where.price = {
      gte: minPrice,
    };
  }

  if (maxPrice !== undefined) {
    where.price = {
      lte: maxPrice,
    };
  }

  if (minPrice !== undefined && maxPrice !== undefined) {
    where.price = {
      gte: minPrice,
      lte: maxPrice,
    };
  }

  if (categoryId) {
    where.categoryId = categoryId;
  }

  const productRequests = await prisma.productRequest.findMany({
    where,
    take: limit,
    skip: (page - 1) * limit,
    orderBy: {
      ...(sort === "LATEST" && { createdAt: "desc" }),
      ...(sort === "OLDEST" && { createdAt: "asc" }),
    },
    select: {
      ...userSelector.productRequest,
      category: {
        select: {
          ...publicSelector.category,
        },
      },
      products: {
        where: {
          category: {
            status: "APPROVED",
            isDeleted: false,
          },
          vendor: {
            auth: {
              status: "APPROVED",
              isVerified: true,
              isDeleted: false,
            },
          },
          isDeleted: false,
        },
        select: {
          ...publicSelector.product,
        },
      },
    },
  });

  const total = await prisma.productRequest.count({ where });
  const pages = Math.ceil(total / limit);

  return {
    productRequests,
    total,
    pages,
    limit,
    page,
  };
}

/**
 * Create a new product request
 */
async function createProductRequestService({
  userId,
  data,
  files,
}: {
  userId: string;
  data: zod.infer<typeof createProductRequestBodySchema>;
  files: Express.Multer.File[];
}) {
  const user = await prisma.user.findUnique({
    where: { authId: userId },
    select: { id: true },
  });

  if (!user) {
    throw new NotFoundResponse("Failed to create product request");
  }

  if (data.categoryId) {
    const category = await prisma.category.findUnique({
      where: {
        id: data.categoryId,
        status: "APPROVED",
        isDeleted: false,
      },
      select: {
        id: true,
      },
    });

    if (!category) {
      throw new BadResponse("Failed to create product request");
    }
  }

  if (!files) {
    throw new BadResponse("Pictures are required");
  }

  const pictureIds: string[] = [];

  if (files && Array.isArray(files)) {
    for (const file of files) {
      const pictureId = await addFile({ file });
      pictureIds.push(pictureId);
    }
  }

  const productRequest = await prisma.productRequest.create({
    data: { ...data, pictureIds, userId: user.id },
    select: {
      ...userSelector.productRequest,
      category: {
        select: {
          ...publicSelector.category,
        },
      },
      products: {
        select: {
          ...publicSelector.product,
        },
      },
    },
  });

  return { productRequest };
}

/**
 * Delete a product request
 */
async function deleteProductRequestService({
  userId,
  requestId,
}: {
  userId: string;
  requestId: string;
}) {
  const user = await prisma.user.findUnique({
    where: { authId: userId },
    select: { id: true },
  });

  if (!user) {
    throw new NotFoundResponse("Product request not found");
  }

  const productRequest = await prisma.productRequest.update({
    where: { id: requestId, userId: user.id },
    data: { isDeleted: true },
    select: {
      ...userSelector.productRequest,
      category: {
        select: {
          ...publicSelector.category,
        },
      },
      products: {
        select: {
          ...publicSelector.product,
        },
      },
    },
  });

  if (!productRequest) {
    throw new NotFoundResponse("Product request not found");
  }

  return { productRequest };
}

export {
  getProductRequestsService,
  createProductRequestService,
  deleteProductRequestService,
};
