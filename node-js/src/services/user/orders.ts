import type { DeliveryOption, OrderStatus, Prisma } from "@prisma/client";

import { BadResponse, NotFoundResponse } from "~/lib/error";
import { prisma } from "~/lib/prisma";
import { publicSelector } from "~/selectors/public";
import { vendorSelector } from "~/selectors/vendor";

/**
 * Get orders for a user with filters
 */
async function getOrdersService({
  userId,
  page,
  limit,
  sort,
  status,
  productName,
  vendorName,
  minTotalPrice,
  maxTotalPrice,
  categoryId,
}: {
  userId: string;
  page: number;
  limit: number;
  sort?: "LATEST" | "OLDEST";
  status?: OrderStatus;
  productName?: string;
  vendorName?: string;
  minTotalPrice?: number;
  maxTotalPrice?: number;
  categoryId?: string;
}) {
  if (categoryId) {
    const category = await prisma.category.findUnique({
      where: { id: categoryId, status: "APPROVED", isDeleted: false },
      select: { id: true },
    });

    if (!category) {
      return {
        orders: [],
        total: 0,
        pages: 1,
        limit,
        page,
      };
    }
  }

  const user = await prisma.user.findUnique({
    where: { authId: userId },
    select: {
      id: true,
    },
  });

  if (!user) {
    throw new NotFoundResponse("Order not found");
  }

  const where: Prisma.OrderWhereInput = {
    userId: user.id,
  };

  if (status) {
    where.status = status;
  }

  if (minTotalPrice !== undefined) {
    where.totalPrice = {
      gte: minTotalPrice,
    };
  }

  if (maxTotalPrice !== undefined) {
    where.totalPrice = {
      lte: maxTotalPrice,
    };
  }

  if (minTotalPrice !== undefined && maxTotalPrice !== undefined) {
    where.totalPrice = {
      gte: minTotalPrice,
      lte: maxTotalPrice,
    };
  }

  if (productName) {
    where.orderToProduct = {
      some: {
        product: {
          name: {
            contains: productName,
            mode: "insensitive",
          },
        },
      },
    };
  }

  if (vendorName) {
    where.orderToProduct = {
      some: {
        product: {
          vendor: {
            name: {
              contains: vendorName,
              mode: "insensitive",
            },
          },
        },
      },
    };
  }

  if (categoryId) {
    where.orderToProduct = {
      some: {
        product: {
          categoryId,
        },
      },
    };
  }

  const orders = await prisma.order.findMany({
    where,
    take: limit,
    skip: (page - 1) * limit,
    orderBy: {
      ...(sort === "LATEST" && { createdAt: "desc" }),
      ...(sort === "OLDEST" && { createdAt: "asc" }),
    },
    select: {
      ...publicSelector.order,
      orderToProduct: {
        select: {
          ...publicSelector.orderToProduct,
          product: {
            select: {
              ...publicSelector.product,
              category: {
                select: {
                  ...publicSelector.category,
                },
              },
              vendor: {
                select: {
                  ...vendorSelector.profile,
                },
              },
            },
          },
        },
      },
      deliveryRequest: {
        select: {
          ...publicSelector.deliveryRequest,
        },
      },
    },
  });

  const total = await prisma.order.count({ where });
  const pages = Math.ceil(total / limit);

  return {
    orders,
    total,
    pages,
    limit,
    page,
  };
}

/**
 * Get a single order for a user
 */
async function getOrderService({
  userId,
  orderId,
}: {
  userId: string;
  orderId: string;
}) {
  const user = await prisma.user.findUnique({
    where: { authId: userId },
    select: {
      id: true,
    },
  });

  if (!user) {
    throw new NotFoundResponse("Order not found");
  }

  const order = await prisma.order.findUnique({
    where: {
      id: orderId,
      userId: user.id,
    },
    select: {
      ...publicSelector.order,
      orderToProduct: {
        select: {
          ...publicSelector.orderToProduct,
          product: {
            select: {
              ...publicSelector.product,
              category: {
                select: {
                  ...publicSelector.category,
                },
              },
              vendor: {
                select: {
                  ...vendorSelector.profile,
                },
              },
            },
          },
        },
      },
      deliveryRequest: {
        select: {
          ...publicSelector.deliveryRequest,
        },
      },
    },
  });

  if (!order) {
    throw new NotFoundResponse("Order not found");
  }

  return { order };
}

/**
 * Create a new order for a user
 */
async function createOrderService({
  userId,
  products: items,
  deliveryOption,
}: {
  userId: string;
  products: { productId: string; quantity: number }[];
  deliveryOption: DeliveryOption;
}) {
  const products = await prisma.product.findMany({
    where: {
      id: {
        in: items.map((product) => product.productId),
      },
      isDeleted: false,
      category: {
        status: "APPROVED",
        isDeleted: false,
      },
      vendor: {
        auth: {
          status: "APPROVED",
          isVerified: true,
          isDeleted: false,
        },
      },
    },
    select: {
      ...publicSelector.product,
      category: {
        select: {
          ...publicSelector.category,
        },
      },
      vendor: {
        select: {
          ...vendorSelector.profile,
        },
      },
    },
  });

  if (products.length !== items.length) {
    throw new BadResponse("Failed to create order");
  }

  for (const item of items) {
    const product = products.find((p) => p.id === item.productId);

    if (product && product.stock < item.quantity) {
      throw new BadResponse("Failed to create order");
    }
  }

  const vendorIds = new Set(products.map((product) => product.vendor.id));

  if (vendorIds.size > 1) {
    throw new BadResponse("Failed to create order");
  }

  const totalPrice = products.reduce((totalPrice, product) => {
    const item = items.find((item) => item.productId === product.id);

    return totalPrice + (item?.quantity || 1) * product.price;
  }, 0);

  const user = await prisma.user.findUnique({
    where: { authId: userId },
    select: {
      id: true,
    },
  });

  if (!user) {
    throw new BadResponse("Failed to create order");
  }

  const order = await prisma.$transaction(async (tx) => {
    const newOrder = await tx.order.create({
      data: {
        userId: user.id,
        totalPrice: totalPrice,
        deliveryOption,
      },
      select: {
        ...publicSelector.order,
        orderToProduct: {
          select: {
            ...publicSelector.orderToProduct,
            product: {
              select: {
                ...publicSelector.product,
                category: {
                  select: {
                    ...publicSelector.category,
                  },
                },
                vendor: {
                  select: {
                    ...vendorSelector.profile,
                  },
                },
              },
            },
          },
        },
        deliveryRequest: {
          select: {
            ...publicSelector.deliveryRequest,
          },
        },
      },
    });

    await tx.orderToProduct.createMany({
      data: products.map((product) => {
        const item = items.find((item) => item.productId === product.id);

        if (!item) {
          throw new BadResponse("Failed to create order");
        }

        return {
          orderId: newOrder.id,
          productId: product.id,
          quantity: item.quantity,
        };
      }),
    });

    for (const item of items) {
      await tx.product.update({
        where: { id: item.productId },
        data: { stock: { decrement: item.quantity } },
      });
    }

    return newOrder;
  });

  if (!order) {
    throw new BadResponse("Failed to create order");
  }

  return { order };
}

export { getOrdersService, getOrderService, createOrderService };
