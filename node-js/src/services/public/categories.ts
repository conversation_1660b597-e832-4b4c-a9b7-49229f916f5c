import type { Prisma } from "@prisma/client";

import { prisma } from "~/lib/prisma";
import { publicSelector } from "~/selectors/public";

/**
 * Get all approved and non-deleted categories
 */
async function getCategoriesService({ name }: { name?: string }) {
  const where: Prisma.CategoryWhereInput = {
    status: "APPROVED",
    isDeleted: false,
  };

  if (name) {
    where.name = {
      contains: name,
      mode: "insensitive",
    };
  }

  const categories = await prisma.category.findMany({
    where,
    select: {
      ...publicSelector.category,
    },
  });

  return { categories };
}

export { getCategoriesService };
