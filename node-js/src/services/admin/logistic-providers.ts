import type { Prisma, UserStatus } from "@prisma/client";

import { NotFoundResponse } from "~/lib/error";
import { prisma } from "~/lib/prisma";
import { adminSelector } from "~/selectors/admin";
import { logisticProviderSelector } from "~/selectors/logistic-provider";

/**
 * Get logistic providers with filters
 */
async function getLogisticProvidersService({
  page,
  limit,
  sort,
  email,
  name,
  phone,
  postalCode,
  city,
  address,
  status,
  isVerified,
  isDeleted,
}: {
  page: number;
  limit: number;
  sort?: "LATEST" | "OLDEST";
  email?: string;
  name?: string;
  phone?: string;
  postalCode?: string;
  city?: string;
  address?: string;
  status?: UserStatus;
  isVerified?: boolean;
  isDeleted?: boolean;
}) {
  const where: Prisma.LogisticProviderWhereInput = {};
  const authWhere: Prisma.AuthWhereInput = {};

  if (email) {
    authWhere.email = {
      contains: email,
      mode: "insensitive",
    };
  }

  if (name) {
    where.name = {
      contains: name,
      mode: "insensitive",
    };
  }

  if (phone) {
    where.phone = {
      contains: phone,
      mode: "insensitive",
    };
  }

  if (postalCode) {
    where.postalCode = {
      contains: postalCode,
      mode: "insensitive",
    };
  }

  if (city) {
    where.city = {
      contains: city,
      mode: "insensitive",
    };
  }

  if (address) {
    where.address = {
      contains: address,
      mode: "insensitive",
    };
  }

  if (status) {
    authWhere.status = status;
  }

  if (isVerified !== undefined) {
    authWhere.isVerified = isVerified;
  }

  if (isDeleted !== undefined) {
    authWhere.isDeleted = isDeleted;
  }

  if (Object.keys(authWhere).length > 0) {
    where.auth = authWhere;
  }

  const logisticProviders = await prisma.logisticProvider.findMany({
    where,
    take: limit,
    skip: (page - 1) * limit,
    orderBy: {
      ...(sort === "LATEST" && { createdAt: "desc" }),
      ...(sort === "OLDEST" && { createdAt: "asc" }),
    },
    select: {
      ...logisticProviderSelector.profile,
      auth: {
        select: {
          ...adminSelector.auth,
        },
      },
    },
  });

  const total = await prisma.logisticProvider.count({ where });
  const pages = Math.ceil(total / limit);

  return {
    logisticProviders,
    total,
    pages,
    limit,
    page,
  };
}

/**
 * Get a single logistic provider
 */
async function getLogisticProviderService({
  logisticProviderId,
}: {
  logisticProviderId: string;
}) {
  const logisticProvider = await prisma.logisticProvider.findUnique({
    where: { id: logisticProviderId },
    select: {
      ...logisticProviderSelector.profile,
      auth: {
        select: {
          ...adminSelector.auth,
        },
      },
    },
  });

  if (!logisticProvider) {
    throw new NotFoundResponse("Logistic provider not found");
  }

  return { logisticProvider };
}

/**
 * Update a logistic provider's auth information
 */
async function updateLogisticProviderService({
  logisticProviderId,
  data,
}: {
  logisticProviderId: string;
  data: Prisma.AuthUpdateInput;
}) {
  const logisticProvider = await prisma.logisticProvider.update({
    where: { id: logisticProviderId },
    data: {
      auth: {
        update: data,
      },
    },
    select: {
      ...logisticProviderSelector.profile,
      auth: {
        select: {
          ...adminSelector.auth,
        },
      },
    },
  });

  if (!logisticProvider) {
    throw new NotFoundResponse("Logistic provider not found");
  }

  return { logisticProvider };
}

export {
  getLogisticProvidersService,
  getLogisticProviderService,
  updateLogisticProviderService,
};
