import type { Prisma } from "@prisma/client";

import { prisma } from "~/lib/prisma";
import { publicSelector } from "~/selectors/public";
import { userSelector } from "~/selectors/user";
import { vendorSelector } from "~/selectors/vendor";

/**
 * Get all product requests for a vendor
 */
async function getProductRequestsService({
  vendorId,
  page,
  limit,
  sort,
  name,
  minQuantity,
  minPrice,
  maxPrice,
  categoryId,
}: {
  vendorId: string;
  page: number;
  limit: number;
  sort?: "LATEST" | "OLDEST";
  name?: string;
  minQuantity?: number;
  minPrice?: number;
  maxPrice?: number;
  categoryId?: string;
}) {
  const vendor = await prisma.vendor.findUnique({
    where: { authId: vendorId },
    select: { id: true },
  });

  if (!vendor) {
    return {
      productRequests: [],
      total: 0,
      pages: 1,
      limit,
      page,
    };
  }

  if (categoryId) {
    const category = await prisma.category.findUnique({
      where: { id: categoryId, status: "APPROVED", isDeleted: false },
      select: { id: true },
    });

    if (!category) {
      return {
        productRequests: [],
        total: 0,
        pages: 1,
        limit,
        page,
      };
    }
  }

  const where: Prisma.ProductRequestWhereInput = {
    isDeleted: false,
    products: {
      none: {
        category: {
          status: "APPROVED",
          isDeleted: false,
        },
        isDeleted: false,
        vendorId: vendor.id,
      },
    },
  };

  if (name) {
    where.name = {
      contains: name,
      mode: "insensitive",
    };
  }

  if (minQuantity !== undefined) {
    where.quantity = {
      gte: minQuantity,
    };
  }

  if (minPrice !== undefined) {
    where.price = {
      gte: minPrice,
    };
  }

  if (maxPrice !== undefined) {
    where.price = {
      lte: maxPrice,
    };
  }

  if (minPrice !== undefined && maxPrice !== undefined) {
    where.price = {
      gte: minPrice,
      lte: maxPrice,
    };
  }

  if (categoryId) {
    where.categoryId = categoryId;
  }

  const productRequests = await prisma.productRequest.findMany({
    where,
    take: limit,
    skip: (page - 1) * limit,
    orderBy: {
      ...(sort === "LATEST" && { createdAt: "desc" }),
      ...(sort === "OLDEST" && { createdAt: "asc" }),
    },
    select: {
      ...vendorSelector.productRequest,
      category: {
        select: {
          ...publicSelector.category,
        },
      },
      user: {
        select: {
          ...userSelector.profile,
        },
      },
    },
  });

  const total = await prisma.productRequest.count({ where });
  const pages = Math.ceil(total / limit);

  return {
    productRequests,
    total,
    pages,
    limit,
    page,
  };
}

export { getProductRequestsService };
