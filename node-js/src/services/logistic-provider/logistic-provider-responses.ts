import { BadResponse } from "~/lib/error";
import { prisma } from "~/lib/prisma";
import { logisticProviderSelector } from "~/selectors/logistic-provider";
import { publicSelector } from "~/selectors/public";

/**
 * Create a new logistic provider response for a logistic provider
 */
async function createLogisticProviderResponseService({
  logisticProviderId,
  deliveryRequestId,
  price,
}: {
  logisticProviderId: string;
  deliveryRequestId: string;
  price: number;
}) {
  const logisticProvider = await prisma.logisticProvider.findUnique({
    where: { authId: logisticProviderId },
    select: { id: true },
  });

  if (!logisticProvider) {
    throw new BadResponse("Failed to create offer");
  }

  const deliveryRequest = await prisma.deliveryRequest.update({
    where: {
      id: deliveryRequestId,
      acceptedLogisticProvider: null,
      responses: {
        none: {
          logisticProviderId: logisticProvider.id,
        },
      },
    },
    data: {
      status: "PROPOSED",
    },
    select: {
      id: true,
    },
  });

  if (!deliveryRequest) {
    throw new BadResponse("Failed to create offer");
  }

  const logisticProviderResponse = await prisma.logisticProviderResponse.create(
    {
      data: {
        price,
        deliveryRequest: {
          connect: {
            id: deliveryRequestId,
          },
        },
        logisticProvider: {
          connect: {
            id: logisticProvider.id,
          },
        },
      },
      select: {
        ...publicSelector.logisticProviderResponse,
        deliveryRequest: {
          select: {
            ...publicSelector.deliveryRequest,
          },
        },
        logisticProvider: {
          select: {
            ...logisticProviderSelector.profile,
            auth: {
              select: {
                ...publicSelector.auth,
              },
            },
          },
        },
      },
    },
  );

  return { logisticProviderResponse };
}

export { createLogisticProviderResponseService };
