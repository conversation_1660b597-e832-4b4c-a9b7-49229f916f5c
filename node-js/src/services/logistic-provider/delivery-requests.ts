import type { DeliveryRequestStatus, Prisma } from "@prisma/client";

import { BadResponse } from "~/lib/error";
import { prisma } from "~/lib/prisma";
import { logisticProviderSelector } from "~/selectors/logistic-provider";
import { publicSelector } from "~/selectors/public";
import { userSelector } from "~/selectors/user";
import { vendorSelector } from "~/selectors/vendor";

/**
 * Get all delivery requests for a logistic provider
 */
async function getDeliveryRequestsService({
  logisticProviderId,
  page,
  limit,
  sort,
  minPrice,
  maxPrice,
  isAccepted,
}: {
  logisticProviderId: string;
  page: number;
  limit: number;
  sort?: "LATEST" | "OLDEST";
  minPrice?: number;
  maxPrice?: number;
  isAccepted?: boolean;
}) {
  const logisticProvider = await prisma.logisticProvider.findUnique({
    where: { authId: logisticProviderId },
    select: { id: true },
  });

  if (!logisticProvider) {
    return {
      deliveryRequests: [],
      total: 0,
      pages: 1,
      limit,
      page,
    };
  }

  const where: Prisma.DeliveryRequestWhereInput = {};

  if (minPrice !== undefined && maxPrice !== undefined) {
    where.price = {
      gte: minPrice,
      lte: maxPrice,
    };
  } else if (minPrice !== undefined) {
    where.price = {
      gte: minPrice,
    };
  } else if (maxPrice !== undefined) {
    where.price = {
      lte: maxPrice,
    };
  }

  if (isAccepted) {
    where.acceptedLogisticProviderId = logisticProvider.id;
  } else {
    where.acceptedLogisticProvider = null;
    where.responses = {
      none: {
        logisticProviderId: logisticProvider.id,
      },
    };
  }

  const deliveryRequests = await prisma.deliveryRequest.findMany({
    where,
    take: limit,
    skip: (page - 1) * limit,
    orderBy: {
      ...(sort === "LATEST" && { createdAt: "desc" }),
      ...(sort === "OLDEST" && { createdAt: "asc" }),
    },
    select: {
      ...publicSelector.deliveryRequest,
      order: {
        select: {
          ...publicSelector.order,
          orderToProduct: {
            select: {
              ...publicSelector.orderToProduct,
              product: {
                select: {
                  ...publicSelector.product,
                  category: {
                    select: {
                      ...publicSelector.category,
                    },
                  },
                  vendor: {
                    select: {
                      ...vendorSelector.profile,
                    },
                  },
                },
              },
            },
          },
          user: {
            select: {
              ...userSelector.profile,
            },
          },
        },
      },
      acceptedLogisticProvider: {
        select: {
          ...logisticProviderSelector.profile,
        },
      },
    },
  });

  const total = await prisma.deliveryRequest.count({ where });
  const pages = Math.ceil(total / limit);

  return {
    deliveryRequests,
    total,
    pages,
    limit,
    page,
  };
}

/**
 * Toggle delivery request status for a logistic provider
 */
async function toggleDeliveryRequestStatusService({
  logisticProviderId,
  deliveryRequestId,
  status,
}: {
  logisticProviderId: string;
  deliveryRequestId: string;
  status: DeliveryRequestStatus;
}) {
  const logisticProvider = await prisma.logisticProvider.findUnique({
    where: { authId: logisticProviderId },
    select: { id: true },
  });

  if (!logisticProvider) {
    throw new BadResponse("Failed to toggle delivery request status");
  }

  const deliveryRequest = await prisma.deliveryRequest.update({
    where: {
      id: deliveryRequestId,
      acceptedLogisticProviderId: logisticProvider.id,
    },
    data: {
      status,
    },
    select: {
      ...publicSelector.deliveryRequest,
      order: {
        select: {
          ...publicSelector.order,
          orderToProduct: {
            select: {
              ...publicSelector.orderToProduct,
              product: {
                select: {
                  ...publicSelector.product,
                  category: {
                    select: {
                      ...publicSelector.category,
                    },
                  },
                  vendor: {
                    select: {
                      ...vendorSelector.profile,
                    },
                  },
                },
              },
            },
          },
          user: {
            select: {
              ...userSelector.profile,
            },
          },
        },
      },
      acceptedLogisticProvider: {
        select: {
          ...logisticProviderSelector.profile,
        },
      },
    },
  });

  if (!deliveryRequest) {
    throw new BadResponse("Failed to toggle delivery request status");
  }

  return { deliveryRequest };
}

export { getDeliveryRequestsService, toggleDeliveryRequestStatusService };
