import { Router } from "express";

import { deliveryRequestsRouter } from "~/routers/logistic-provider/delivery-requests";
import { logisticProviderResponsesRouter } from "~/routers/logistic-provider/logistic-provider-responses";
import { profileRouter } from "~/routers/logistic-provider/profile";

const logisticProviderRouter = Router();

logisticProviderRouter.use("/delivery-requests", deliveryRequestsRouter);
logisticProviderRouter.use(
  "/logistic-provider-responses",
  logisticProviderResponsesRouter,
);
logisticProviderRouter.use("/profile", profileRouter);

export { logisticProviderRouter };
