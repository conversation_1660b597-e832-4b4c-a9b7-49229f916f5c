import { Router } from "express";

import {
  addItemToCart,
  clearCartItems,
  getCartItems,
  removeItemFromCart,
  updateCartItem,
} from "~/controllers/user/cart";

const cartRouter = Router();

cartRouter.get("/", getCartItems);

cartRouter.post("/", addItemToCart);

cartRouter.put("/:productId", updateCartItem);

cartRouter.delete("/:productId", removeItemFromCart);

cartRouter.delete("/", clearCartItems);

export { cartRouter };
