import { Router } from "express";

import {
  createProductRequest,
  deleteProductRequest,
  getProductRequests,
} from "~/controllers/user/product-requests";
import { uploadMultiple } from "~/middlewares/upload";

const productRequestsRouter = Router();

productRequestsRouter.get("/", getProductRequests);
productRequestsRouter.post(
  "/",
  uploadMultiple("pictures"),
  createProductRequest,
);
productRequestsRouter.delete("/:id", deleteProductRequest);

export { productRequestsRouter };
