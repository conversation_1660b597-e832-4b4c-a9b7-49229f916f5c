import { Router } from "express";

import {
  getLogisticProvider,
  getLogisticProviders,
  updateLogisticProvider,
} from "~/controllers/admin/logistic-providers";

const logisticProvidersRouter = Router();

logisticProvidersRouter.get("/", getLogisticProviders);

logisticProvidersRouter.get("/:id", getLogisticProvider);

logisticProvidersRouter.put("/:id", updateLogisticProvider);

export { logisticProvidersRouter };
