import { Router } from "express";

import {
  getProduct,
  getProducts,
  toggleProductEcoBuiltVerification,
  toggleProductIsDeleted,
} from "~/controllers/admin/products";

const productsRouter = Router();

productsRouter.get("/", getProducts);

productsRouter.get("/:id", getProduct);

productsRouter.delete("/:id", toggleProductIsDeleted);

productsRouter.put(
  "/:id/ecobuilt-verification",
  toggleProductEcoBuiltVerification,
);

export { productsRouter };
