{"name": "@ecobuilt/node-js", "version": "0.1.0", "private": true, "scripts": {"biome:check": "biome check", "biome:safe": "biome check --write", "biome:unsafe": "biome check --write --unsafe", "prisma:generate": "prisma generate", "prisma:studio": "prisma studio", "rename": "tsx src/scripts/rename.ts", "dev": "tsx --watch src/server.ts", "start": "tsx src/server.ts", "postinstall": "pnpm prisma:generate"}, "packageManager": "pnpm@10.10.0", "dependencies": {"@aws-sdk/client-s3": "^3.842.0", "@prisma/client": "^6.11.1", "argon2": "^0.41.1", "axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "2.0.1", "node-cron": "^3.0.3", "nodemailer": "^6.10.1", "socket.io": "^4.8.1", "uuid": "^11.1.0", "zod": "^3.25.71"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/multer": "^1.4.13", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "prisma": "^6.11.1", "tsx": "^4.20.3"}}